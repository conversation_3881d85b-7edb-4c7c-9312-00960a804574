workflow:
  rules:
    - if: $CI_COMMIT_BRANCH == "production" || $CI_COMMIT_BRANCH == "stage" || $CI_COMMIT_BRANCH == "develop" || $TEST == "true"
      when: always
    - when: never

variables:
  PROJECT_DEFAULT_DIRECTORY: /opt/$CI_PROJECT_NAMESPACE
  STAGE_SERVER_USER: ouser
  PRODUCTION_SERVER_USER: ouser
  SSH_PORT: 22
  STAGE_PORT: 5022
  PRODUCTION_PORT: 5023
  STAGE_SERVER_HOST: **************
  STAGE_APP_API_BASE_URL: http://alert-center-backend-stage.synappse.stinascloud.ir/api/v1/
  STAGE_ENDPOINT: http://alert-center-frontend-stage.synappse.stinascloud.ir/
  PRODUCTION_SERVER_HOST: **************
  PRODUCTION_APP_API_BASE_URL: https://api.hodhod-app.ir
  PRODUCTION_ENDPOINT: https://panel.hodhod-app.ir
  SSH_COMMAND: ssh -o StrictHostKeyChecking=no -p $SSH_PORT -i deploy-key.pem
  SCP_COMMAND: scp -P $SSH_PORT -o StrictHostKeyChecking=no -i deploy-key.pem

stages:
  - build
  - deploy

build_and_push_image:
  stage: build
  tags:
    - builder
  before_script:
    - echo "$HARBOR_PASSWORD" | docker login --password-stdin --username "$HARBOR_USER" -- "$HARBOR_REGISTRY"
  script:
    - docker build
      -t $HARBOR_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$CI_PIPELINE_IID
      -f Dockerfile .
    - docker push $HARBOR_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$CI_PIPELINE_IID
  rules:
    - if: $CI_COMMIT_BRANCH == "stage" || $CI_COMMIT_BRANCH == "develop"
      when: on_success
    - if: $CI_COMMIT_BRANCH == "production" || $TEST == "true"
      when: manual

.deploy_template:
  stage: deploy
  tags:
    - builder
  needs:
    - job: build_and_push_image
  variables:
    SERVER_USER: ""
    APP_API_BASE_URL: ""
    FOLDER_NAME: ""
    SERVER_HOST: ""
    DEPLOY_ENV: ""
    APP_PORT: ""
    ENDPOINT: ""
    IMAGE_TAG_SUFFIX: ""
  before_script:
    - echo "$HARBOR_PASSWORD" | docker login --password-stdin --username "$HARBOR_USER" -- "$HARBOR_REGISTRY"
    - docker pull $HARBOR_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:$CI_PIPELINE_IID
    - echo "DC_FOLDER_NAME=$FOLDER_NAME" > .env
    - echo "DC_DEPLOY_ENV=$DEPLOY_ENV" >> .env
    - echo "DC_VITE_API_BASE_URL=$APP_API_BASE_URL" >> .env
    - echo "DC_APP_PORT=$APP_PORT" >> .env
    - echo "DC_ENVIRONMENT_STATE=$DEPLOY_ENV" >> .env
    - echo "DC_IMAGE_TAG=$CI_PIPELINE_IID" >> .env
    - echo "DC_IMAGE_NAME=$HARBOR_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME" >> .env
    - cat .env
    - cat $SSH_PRIVATE_KEY | awk '{gsub(/\\n/,"\n")}1' > deploy-key.pem
    - chmod 400 deploy-key.pem
  script:
    - $SSH_COMMAND $SERVER_USER@$SERVER_HOST "sudo mkdir -p $PROJECT_DEFAULT_DIRECTORY/$FOLDER_NAME $PROJECT_DEFAULT_DIRECTORY/$FOLDER_NAME/backup_compose_file && sudo chown -R $SERVER_USER:$SERVER_USER $PROJECT_DEFAULT_DIRECTORY/$FOLDER_NAME $PROJECT_DEFAULT_DIRECTORY/$FOLDER_NAME/backup_compose_file"
    - $SCP_COMMAND .env ./docker-compose.yml ./deployments/scripts/update.sh $SERVER_USER@$SERVER_HOST:$PROJECT_DEFAULT_DIRECTORY/$FOLDER_NAME
    - $SSH_COMMAND $SERVER_USER@$SERVER_HOST "
      echo "$HARBOR_PASSWORD" | docker login --password-stdin --username "$HARBOR_USER" -- "$HARBOR_REGISTRY" &&
      cd $PROJECT_DEFAULT_DIRECTORY/$FOLDER_NAME &&

      ./update.sh $DEPLOY_ENV docker-compose.yml "
  environment:
    name: $DEPLOY_ENV
    url: $ENDPOINT

#deploy_to_stage:
#  extends: .deploy_template
#  variables:
#    SERVER_USER: $STAGE_SERVER_USER
#    APP_API_BASE_URL: $STAGE_APP_API_BASE_URL
#    FOLDER_NAME: $CI_PROJECT_NAME-stage
#    SERVER_HOST: $STAGE_SERVER_HOST
#    DEPLOY_ENV: stage
#    APP_PORT: $STAGE_PORT
#    ENDPOINT: $STAGE_ENDPOINT
#  rules:
#    - if: $CI_COMMIT_BRANCH == "stage" || $CI_COMMIT_BRANCH == "develop"
#      when: on_success
#    - if: $TEST == "true"
#      when: manual
