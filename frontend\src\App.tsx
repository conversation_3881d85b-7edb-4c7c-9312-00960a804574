import { RouterProvider } from 'react-router-dom';
import { AuthProvider } from '@/context/AuthContext';
import { router } from './routes';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import SkipAuthIndicator from '@/components/common/SkipAuthIndicator';
import { ToastContainer } from 'react-toastify';

function App() {
  return (
    <>
      <SkipAuthIndicator />
      <ErrorBoundary>
        <AuthProvider>
          <RouterProvider router={router} />
        </AuthProvider>
        <ToastContainer
          position="top-right"
          theme="dark"
          autoClose={5000}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          rtl
          newestOnTop
          closeOnClick={false}
        />
      </ErrorBoundary>
    </>
  );
}

export default App;
