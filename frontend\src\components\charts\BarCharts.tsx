import React from 'react';
import Responsive<PERSON>hartWrapper from './ResponsiveChartWrapper';
import {
  createChartConfig,
  AXIS_CONFIG,
  RESPONSIVE_RULES,
} from './chartConfig';

// Types for different bar chart data formats
export interface BarChartSeries {
  name: string;
  data: number[];
}

export interface BarChartProps {
  data?: BarChartSeries[] | number[];
  categories?: string[];
  title?: string;
  className?: string;
  showLegend?: boolean;
  horizontal?: boolean;
  stacked?: boolean;
  grouped?: boolean;
}

/**
 * نمودار میله‌ای ساده
 */
export const SimpleBarChart: React.FC<BarChartProps> = ({
  data = [29.9, 71.5, 106.4, 129.2, 144.0, 176.0],
  categories = ['فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور'],
  title = 'نمودار میله‌ای ساده',
  className = '',
  showLegend = false,
  horizontal = false,
}) => {
  const processedData: BarChartSeries[] =
    Array.isArray(data) && typeof data[0] === 'number'
      ? [{ name: 'مقادیر', data: data as number[] }]
      : (data as BarChartSeries[]);

  const options = createChartConfig(
    {
      chart: {
        type: horizontal ? 'bar' : 'column',
      },
      title: {
        text: title,
      },
      xAxis: {
        ...AXIS_CONFIG.xAxis,
        categories: categories,
      },
      yAxis: {
        ...AXIS_CONFIG.yAxis,
        title: {
          text: 'مقدار',
          style: AXIS_CONFIG.yAxis.title?.style,
        },
      },
      series: processedData.map((series) => ({
        ...series,
        type: horizontal ? 'bar' : 'column',
      })),
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};

/**
 * نمودار میله‌ای تجمعی
 */
export const StackedBarChart: React.FC<BarChartProps> = ({
  data = [
    { name: 'سری اول', data: [5, 3, 4, 7, 2] },
    { name: 'سری دوم', data: [2, 2, 3, 2, 1] },
    { name: 'سری سوم', data: [3, 4, 4, 2, 5] },
  ],
  categories = ['دسته ۱', 'دسته ۲', 'دسته ۳', 'دسته ۴', 'دسته ۵'],
  title = 'نمودار میله‌ای تجمعی',
  className = '',
  showLegend = true,
  horizontal = false,
}) => {
  const processedData = data as BarChartSeries[];

  const options = createChartConfig(
    {
      chart: {
        type: horizontal ? 'bar' : 'column',
      },
      title: {
        text: title,
      },
      xAxis: {
        ...AXIS_CONFIG.xAxis,
        categories: categories,
      },
      yAxis: {
        ...AXIS_CONFIG.yAxis,
        title: {
          text: 'مقدار',
          style: AXIS_CONFIG.yAxis.title?.style,
        },
      },
      plotOptions: {
        column: {
          stacking: 'normal',
          dataLabels: {
            enabled: false,
          },
        },
        bar: {
          stacking: 'normal',
          dataLabels: {
            enabled: false,
          },
        },
      },
      series: processedData.map((series) => ({
        ...series,
        type: horizontal ? 'bar' : 'column',
      })),
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  console.log(options);

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};

/**
 * نمودار میله‌ای مقایسه‌ای (گروه‌بندی شده)
 */
export const GroupedBarChart: React.FC<BarChartProps> = ({
  data = [
    { name: 'سال ۱۴۰۲', data: [49.9, 71.5, 106.4, 129.2, 144.0, 176.0] },
    { name: 'سال ۱۴۰۳', data: [83.6, 78.8, 98.5, 93.4, 106.0, 84.5] },
  ],
  categories = ['فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور'],
  title = 'نمودار میله‌ای مقایسه‌ای',
  className = '',
  showLegend = true,
  horizontal = false,
}) => {
  const processedData = data as BarChartSeries[];

  const options = createChartConfig(
    {
      chart: {
        type: horizontal ? 'bar' : 'column',
      },
      title: {
        text: title,
      },
      xAxis: {
        ...AXIS_CONFIG.xAxis,
        categories: categories,
      },
      yAxis: {
        ...AXIS_CONFIG.yAxis,
        title: {
          text: 'مقدار',
          style: AXIS_CONFIG.yAxis.title?.style,
        },
      },
      plotOptions: {
        column: {
          grouping: true,
          shadow: false,
          borderWidth: 0,
          pointPadding: 0.1,
          groupPadding: 0.1,
        },
        bar: {
          grouping: true,
          shadow: false,
          borderWidth: 0,
          pointPadding: 0.1,
          groupPadding: 0.1,
        },
      },
      series: processedData.map((series) => ({
        ...series,
        type: horizontal ? 'bar' : 'column',
      })),
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};

// Aliases for horizontal versions
export const HorizontalBarChart: React.FC<BarChartProps> = (props) => (
  <SimpleBarChart {...props} horizontal={true} />
);

export const HorizontalStackedBarChart: React.FC<BarChartProps> = (props) => (
  <StackedBarChart {...props} horizontal={true} />
);

export const HorizontalGroupedBarChart: React.FC<BarChartProps> = (props) => (
  <GroupedBarChart {...props} horizontal={true} />
);
