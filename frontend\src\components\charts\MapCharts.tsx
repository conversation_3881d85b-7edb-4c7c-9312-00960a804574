import React, { memo } from 'react';
import {
  ComposableMap,
  Geographies,
  Geography,
  ZoomableGroup,
} from 'react-simple-maps';

// Import GeoJSON data
import iranProvinces from '@/assets/geojson/iran_provinces.json';
import worldCountries from '@/assets/geojson/countries_border.json';

// Types for map data
export interface MapData {
  key: string;
  value: number;
  name?: string;
}

export interface WorldMapData {
  code: string;
  value: number;
  name?: string;
}

// Color scale function
const getColor = (value: number, min: number, max: number) => {
  const ratio = (value - min) / (max - min);
  const colors = ['#004748', '#026B6E', '#048F92', '#5BF7FA'];
  const index = Math.floor(ratio * (colors.length - 1));
  return colors[index] || colors[0];
};

/**
 * Iran Map Chart using react-simple-maps
 */
export const IranMapChart: React.FC<{
  data?: MapData[];
  title?: string;
  className?: string;
  showLegend?: boolean;
}> = memo(
  ({
    data = [
      { key: 'tehran', value: 85, name: 'تهران' },
      { key: 'isfahan', value: 72, name: 'اصفهان' },
      { key: 'fars', value: 68, name: 'فارس' },
      { key: 'khorasan-razavi', value: 59, name: 'خراسان رضوی' },
      { key: 'east-azerbaijan', value: 45, name: 'آذربایجان شرقی' },
    ],
    title = 'نقشه ایران',
    className = '',
    showLegend = false,
  }) => {
    // Create a map of data for quick lookup
    const dataMap = new Map(data.map((item) => [item.key, item]));

    // Calculate min/max for color scaling
    const values = data.map((d) => d.value);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);

    return (
      <div
        className={`h-full w-full rounded-[5px] border border-[#323538] bg-[#181A1B] p-2 ${className}`}
      >
        {title && (
          <div className="p-3 text-center font-medium text-white">{title}</div>
        )}
        <div className={title ? 'h-[calc(100%-60px)]' : 'h-full'}>
          <ComposableMap
            projection="geoMercator"
            projectionConfig={{
              scale: 1000,
              center: [54, 32], // Center on Iran
            }}
            style={{
              width: '100%',
              height: '100%',
            }}
          >
            <ZoomableGroup>
              <Geographies geography={iranProvinces}>
                {({ geographies }) =>
                  geographies.map((geo) => {
                    const provinceKey = geo.properties.name_en
                      ?.toLowerCase()
                      .replace(/\s+/g, '-');
                    const provinceData = dataMap.get(provinceKey);
                    const fillColor = provinceData
                      ? getColor(provinceData.value, minValue, maxValue)
                      : '#2D3748';

                    return (
                      <Geography
                        key={geo.rsmKey}
                        geography={geo}
                        fill={fillColor}
                        stroke="#4A5568"
                        strokeWidth={0.5}
                        style={{
                          default: {
                            outline: 'none',
                          },
                          hover: {
                            fill: '#5BF7FA',
                            outline: 'none',
                            cursor: 'pointer',
                          },
                          pressed: {
                            fill: '#048F92',
                            outline: 'none',
                          },
                        }}
                      />
                    );
                  })
                }
              </Geographies>
            </ZoomableGroup>
          </ComposableMap>
        </div>

        {showLegend && (
          <div className="mt-2 flex items-center justify-center space-x-2 text-xs text-white">
            <span>کم</span>
            <div className="flex space-x-1">
              {['#004748', '#026B6E', '#048F92', '#5BF7FA'].map((color, i) => (
                <div
                  key={i}
                  className="h-3 w-6"
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
            <span>زیاد</span>
          </div>
        )}
      </div>
    );
  }
);

/**
 * World Map Chart using react-simple-maps
 */
export const WorldMapChart: React.FC<{
  data?: WorldMapData[];
  title?: string;
  className?: string;
  showLegend?: boolean;
}> = memo(
  ({
    data = [
      { code: 'IR', value: 100, name: 'ایران' },
      { code: 'US', value: 85, name: 'آمریکا' },
      { code: 'DE', value: 70, name: 'آلمان' },
      { code: 'FR', value: 65, name: 'فرانسه' },
      { code: 'GB', value: 60, name: 'انگلستان' },
      { code: 'JP', value: 55, name: 'ژاپن' },
      { code: 'CN', value: 90, name: 'چین' },
    ],
    title = 'نقشه جهان',
    className = '',
    showLegend = false,
  }) => {
    // Create a map of data for quick lookup
    const dataMap = new Map(data.map((item) => [item.code, item]));

    // Calculate min/max for color scaling
    const values = data.map((d) => d.value);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);

    return (
      <div
        className={`h-full w-full rounded-[5px] border border-[#323538] bg-[#181A1B] p-2 ${className}`}
      >
        {title && (
          <div className="p-3 text-center font-medium text-white">{title}</div>
        )}
        <div className={title ? 'h-[calc(100%-60px)]' : 'h-full'}>
          <ComposableMap
            projection="geoNaturalEarth1"
            style={{
              width: '100%',
              height: '100%',
            }}
          >
            <ZoomableGroup>
              <Geographies geography={worldCountries}>
                {({ geographies }) =>
                  geographies.map((geo) => {
                    const countryCode = geo.properties.ISO_A2;
                    const countryData = dataMap.get(countryCode);
                    const fillColor = countryData
                      ? getColor(countryData.value, minValue, maxValue)
                      : '#2D3748';

                    return (
                      <Geography
                        key={geo.rsmKey}
                        geography={geo}
                        fill={fillColor}
                        stroke="#4A5568"
                        strokeWidth={0.5}
                        style={{
                          default: {
                            outline: 'none',
                          },
                          hover: {
                            fill: '#5BF7FA',
                            outline: 'none',
                            cursor: 'pointer',
                          },
                          pressed: {
                            fill: '#048F92',
                            outline: 'none',
                          },
                        }}
                      />
                    );
                  })
                }
              </Geographies>
            </ZoomableGroup>
          </ComposableMap>
        </div>

        {showLegend && (
          <div className="mt-2 flex items-center justify-center space-x-2 text-xs text-white">
            <span>Low</span>
            <div className="flex space-x-1">
              {['#004748', '#026B6E', '#048F92', '#5BF7FA'].map((color, i) => (
                <div
                  key={i}
                  className="h-3 w-6"
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
            <span>High</span>
          </div>
        )}
      </div>
    );
  }
);

IranMapChart.displayName = 'IranMapChart';
WorldMapChart.displayName = 'WorldMapChart';
