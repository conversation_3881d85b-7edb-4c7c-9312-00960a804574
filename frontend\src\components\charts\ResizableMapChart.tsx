// @ts-nocheck

import React from 'react';
import Highcharts from 'highcharts/highmaps';
import iranMap from '@highcharts/map-collection/countries/ir/ir-all.geo.json';
import ResponsiveChartWrapper from './ResponsiveChartWrapper';

interface MapDataPoint {
  key: string;
  value: number;
}

interface ResizableMapChartProps {
  data?: MapDataPoint[];
  title?: string;
  width?: number;
  height?: number;
  className?: string;
}

const ResizableMapChart: React.FC<ResizableMapChartProps> = ({
  data,
  title = 'نقشه ایران',
  className = '',
}) => {
  // Generate default data if not provided
  const mapData =
    data ||
    iranMap.features.map((f: any) => ({
      key: f.properties['hc-key'],
      value: Math.floor(Math.random() * 100),
    }));

  const options: Highcharts.Options = {
    chart: {
      map: iranMap as any,
      backgroundColor: 'transparent',
      zoomType: undefined,
      panning: false,
      // Remove fixed dimensions to allow dynamic resizing
      animation: false,
    },
    mapNavigation: {
      enabled: false,
      enableButtons: false,
      enableMouseWheelZoom: false,
      enableTouchZoom: false,
      enableDoubleClickZoom: false,
      buttonOptions: {
        enabled: false,
        verticalAlign: 'bottom',
      },
    },
    title: {
      text: title || undefined,
      style: {
        color: '#ffffff',
        fontFamily: 'iranyekanx',
        fontSize: '14px',
      },
    },
    legend: {
      enabled: false,
    },
    credits: {
      enabled: false,
    },
    colorAxis: {
      min: 0,
      max: 100,
      stops: [
        [0, '#CCF4F5'],
        [0.5, '#05A0A3'],
        [1, '#026B6E'],
      ],
      labels: {
        style: {
          color: '#ffffff',
          fontSize: '10px',
        },
      },
    },
    tooltip: {
      backgroundColor: '#1f2937',
      borderColor: '#374151',
      style: {
        color: '#ffffff',
        fontSize: '10px',
      },
    },
    series: [
      {
        type: 'map',
        data: mapData,
        name: 'Value',
        states: {
          hover: {
            color: '#BADA55',
          },
        },
        dataLabels: {
          enabled: false,
        },
        animation: false,
      },
    ],
    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 300,
          },
          chartOptions: {
            title: {
              style: {
                fontSize: '12px',
              },
            },
            colorAxis: {
              labels: {
                style: {
                  fontSize: '8px',
                },
              },
            },
          },
        },
      ],
    },
  };

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};

export default ResizableMapChart;
