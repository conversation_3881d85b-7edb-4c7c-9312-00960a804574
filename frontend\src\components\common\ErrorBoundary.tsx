import React, { Component, ReactNode } from 'react';
import { AlertTriangle } from 'lucide-react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex min-h-screen items-center justify-center bg-neutral-900 p-4 text-white">
          <div className="w-full max-w-md space-y-6 rounded-xl border border-neutral-700 bg-neutral-800 p-8 text-center shadow-2xl">
            <div className="flex justify-center">
              <AlertTriangle className="h-12 w-12 animate-pulse text-yellow-400" />
            </div>
            <h1 className="text-3xl font-semibold">Something went wrong</h1>
            <p className="text-neutral-400">
              We're sorry, but an unexpected error has occurred.
            </p>

            {this.state.error && (
              <details className="max-h-40 overflow-y-auto border-t border-neutral-700 pt-4 text-sm break-words whitespace-pre-wrap text-neutral-500">
                {this.state.error.message}
              </details>
            )}

            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center justify-center rounded-md bg-blue-600 px-5 py-2.5 font-medium text-white transition-colors hover:bg-blue-700"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
