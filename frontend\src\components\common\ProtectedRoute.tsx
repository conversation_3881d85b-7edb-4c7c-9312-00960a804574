import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';

const mode = import.meta.env.MODE;


interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
}) => {
  const { isAuthenticated, user, isLoading } = useAuth();
  const location = useLocation();

  // Check if authentication should be skipped
  const skipAuth =   mode === 'development'
    ? import.meta.env.VITE_API_BASE_URL === 'true'
    : window.env.VITE_API_BASE_URL === 'true';


  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="border-primary-500 h-32 w-32 animate-spin rounded-full border-b-2"></div>
      </div>
    );
  }

  // Skip authentication check if flag is set
  if (skipAuth) {
    return <>{children}</>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  if (requiredRole && user?.role !== requiredRole) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};
