import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import WidgetForm from '@/components/dashboard/WidgetForm';
import {
  createWidget,
  updateWidget,
  getWidgetById,
} from '@/services/dashboardService';
import { CreateWidgetPayload, Widget } from '@/types/dashboard';

interface WidgetFormPageProps {
  mode: 'create' | 'edit';
}

export default function WidgetFormPage({ mode }: WidgetFormPageProps) {
  const { dashboardId, widgetId } = useParams<{
    dashboardId: string;
    widgetId?: string;
  }>();
  const navigate = useNavigate();
  const [widget, setWidget] = useState<Widget | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  // Load widget data for edit mode
  useEffect(() => {
    if (mode === 'edit' && dashboardId && widgetId) {
      const loadWidget = async () => {
        try {
          setLoading(true);
          setError('');
          const widgetData = await getWidgetById(dashboardId, widgetId);
          setWidget(widgetData);
        } catch (err) {
          const errorMessage =
            err instanceof Error ? err.message : 'خطا در دریافت اطلاعات ویجت';
          setError(errorMessage);
          console.error('Error loading widget:', err);
        } finally {
          setLoading(false);
        }
      };

      loadWidget();
    }
  }, [mode, dashboardId, widgetId]);

  if (!dashboardId) {
    return <div>Dashboard ID not found</div>;
  }

  if (mode === 'edit' && !widgetId) {
    return <div>Widget ID not found</div>;
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <p className="text-gray-600">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 text-red-600">
            <svg
              className="mx-auto mb-2 h-12 w-12"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <p className="mb-4 text-red-600">{error}</p>
          <button
            onClick={() => navigate(-1)}
            className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
          >
            بازگشت
          </button>
        </div>
      </div>
    );
  }

  // Wait for widget data in edit mode
  if (mode === 'edit' && !widget) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <p className="text-gray-600">در حال بارگذاری اطلاعات ویجت...</p>
        </div>
      </div>
    );
  }

  const breadcrumbItems =
    mode === 'create'
      ? [
          // { label: 'داشبورد', href: '/dashboard' },
          {
            label: `داشبورد ${widget?.title || ''}`,
            href: `/dashboard/${dashboardId}`,
          },
          { label: 'ایجاد گزارش جدید' },
        ]
      : [
          // { label: 'داشبورد', href: '/dashboard' },
          {
            label: `داشبورد ${widget?.title || ''}`,
            href: `/dashboard/${dashboardId}`,
          },
          { label: 'ویرایش گزارش' },
        ];

  const handleSubmit = async (payload: CreateWidgetPayload) => {
    if (mode === 'create') {
      await createWidget(dashboardId, payload);
    } else if (mode === 'edit' && widgetId) {
      await updateWidget(dashboardId, widgetId, payload);
    }
  };

  const submitButtonText = mode === 'create' ? 'ایجاد گزارش' : 'ذخیره تغییرات';
  const submittingText =
    mode === 'create' ? 'در حال ایجاد...' : 'در حال ذخیره...';

  return (
    <WidgetForm
      dashboardId={dashboardId}
      widgetId={widgetId}
      initialData={widget}
      breadcrumbItems={breadcrumbItems}
      onSubmit={handleSubmit}
      submitButtonText={submitButtonText}
      submittingText={submittingText}
    />
  );
}
