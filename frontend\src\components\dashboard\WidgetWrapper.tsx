import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { CHART_TYPE_MAPPING, ChartType } from '@/constants/chartTypes';
import { Widget, Dashboard } from '@/types/dashboard';
import { useWidgetData } from '@/hooks/useWidgetData';
import WidgetSkeleton from '@/components/ui/WidgetSkeleton';

import {
  CornersOutIcon,
  ArrowCounterClockwiseIcon,
  PencilIcon,
  TrashSimpleIcon,
} from '@phosphor-icons/react';

interface WidgetWrapperProps {
  widget: Widget;
  dashboard: Dashboard;
  className?: string;
  onWidgetDeleted?: (widgetId: string | number) => void;
  onFullscreen?: (widget: Widget) => void;
  onShowDeleteModal?: (widget: Widget) => void;
}

const WidgetWrapper: React.FC<WidgetWrapperProps> = ({
  widget,
  dashboard,
  className = '',
  onFullscreen,
  onShowDeleteModal,
}) => {
  const navigate = useNavigate();
  const { data, loading, error, refetch } = useWidgetData({
    widget,
    dashboard,
  });
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target as Node)
      ) {
        setIsMenuVisible(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleMouseEnter = () => {
    setIsMenuVisible(true);
  };

  const handleMouseLeave = () => {
    // Add a small delay to prevent menu from disappearing when moving to menu
    setTimeout(() => {
      if (
        !menuRef.current?.matches(':hover') &&
        !wrapperRef.current?.matches(':hover')
      ) {
        setIsMenuVisible(false);
      }
    }, 100);
  };

  const handleFullscreen = () => {
    onFullscreen?.(widget);
    // setIsMenuVisible(false);
  };

  const handleRefresh = async () => {
    await refetch();
    setIsMenuVisible(false);
  };

  const handleEdit = () => {
    // Navigate to widget edit page (similar to dashboard edit but for widget)
    navigate(`/dashboard/${dashboard.id}/widget/${widget.id}/edit`);
    // setIsMenuVisible(false);
  };

  const handleDelete = () => {
    onShowDeleteModal?.(widget);
    // setIsMenuVisible(false);
  };

  const menuItems = [
    {
      id: 'fullscreen',
      label: 'تمام صفحه',
      icon: CornersOutIcon,
      onClick: handleFullscreen,
    },
    {
      id: 'refresh',
      label: 'بروزرسانی داده',
      icon: ArrowCounterClockwiseIcon,
      onClick: handleRefresh,
    },
    {
      id: 'edit',
      label: 'ویرایش',
      icon: PencilIcon,
      onClick: handleEdit,
    },
    {
      id: 'delete',
      label: 'حذف',
      icon: TrashSimpleIcon,
      onClick: handleDelete,
      variant: 'danger' as const,
    },
  ];

  // Show loading skeleton while data is being fetched
  if (loading) {
    return <WidgetSkeleton className={className} />;
  }

  // Show error state
  if (error) {
    return (
      <div
        className={`flex h-full w-full items-center justify-center rounded-lg bg-red-900/20 p-4 ${className}`}
      >
        <div className="text-center text-red-400">
          <div className="mb-2 text-sm font-medium">
            خطا در بارگذاری داده‌ها
          </div>
          <div className="text-xs opacity-80">{error}</div>
        </div>
      </div>
    );
  }

  // Get the chart component
  const ChartComponent = CHART_TYPE_MAPPING[widget.chart_type as ChartType];

  if (!ChartComponent) {
    return (
      <div
        className={`flex h-full w-full items-center justify-center rounded-lg bg-gray-800/50 p-4 ${className}`}
      >
        <div className="text-center text-gray-400">
          <div className="text-sm font-medium">نوع نمودار پشتیبانی نمی‌شود</div>
          <div className="text-xs opacity-80">{widget.chart_type}</div>
        </div>
      </div>
    );
  }

  // Render the chart with data and side menu
  return (
    <div
      ref={wrapperRef}
      className={`relative h-full w-full p-1 ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <ChartComponent
        title={widget.title}
        className="h-full w-full"
        {...data}
      />

      {/* Side Menu */}
      <AnimatePresence>
        {isMenuVisible && (
          <motion.div
            ref={menuRef}
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 48, opacity: 1 }}
            exit={{ x: 0, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="absolute top-1/2 left-1 z-50 h-full -translate-y-1/2 transform py-1"
            onMouseEnter={() => setIsMenuVisible(true)}
            onMouseLeave={handleMouseLeave}
          >
            <div className="h-full bg-[#2C2C2C80] px-1 py-2 shadow-xl backdrop-blur-sm">
              <div className="flex flex-col space-y-1">
                {menuItems.map((item) => (
                  <button
                    key={item.id}
                    onClick={item.onClick}
                    className={`flex items-center space-x-2 rounded-md px-3 py-2 text-lg font-medium transition-colors ${
                      item.variant === 'danger'
                        ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                        : 'text-gray-300 hover:bg-gray-800/50 hover:text-white'
                    }`}
                    title={item.label}
                  >
                    <item.icon className="h-5 w-5" />
                    {/* <span className="whitespace-nowrap">{item.label}</span> */}
                  </button>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default WidgetWrapper;
