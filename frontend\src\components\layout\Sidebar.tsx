import {
  RectangleGroupIcon,
  UsersIcon,
  Cog6ToothIcon,
  BellAlertIcon,
  QuestionMarkCircleIcon,
  Bars3Icon,
  XMarkIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
} from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/utils/utlis';
import { useEditMode } from '@/context/EditModeContext';

const menuItems = [
  { name: 'داشبورد', icon: RectangleGroupIcon, href: '/dashboard' },
  { name: 'پروفایل کاربری', icon: UsersIcon, href: '' },
  { name: 'تنظیمات', icon: Cog6ToothIcon, href: '' },
  { name: 'هشدارها', icon: BellAlertIcon, href: '' },
  { name: 'کم<PERSON>', icon: QuestionMarkCircleIcon, href: '' },
];

interface SidebarProps {
  isMiniMode?: boolean;
  forceMiniMode?: boolean;
  onToggleMiniMode?: () => void;
}

export default function Sidebar({
  isMiniMode = false,
  forceMiniMode = false,
  onToggleMiniMode,
}: SidebarProps) {
  const location = useLocation();
  const { isEditMode } = useEditMode();
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  // Determine if sidebar should be in mini mode
  const shouldBeMini = !isMobile && (isMiniMode || forceMiniMode);
  const isExpanded = shouldBeMini && isHovered;

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (isMobile) {
      setIsOpen(false);
    }
  }, [location.pathname, isMobile]);

  const isLinkActive = (href: string) => {
    if (!href) return false;
    return location.pathname.startsWith(href);
  };

  const handleMouseEnter = () => {
    if (shouldBeMini && !isEditMode) {
      setIsHovered(true);
    }
  };

  const handleMouseLeave = () => {
    if (shouldBeMini && !isEditMode) {
      setIsHovered(false);
    }
  };

  return (
    <>
      {isMobile && isOpen && (
        <div
          className="bg-opacity-50 fixed inset-0 z-40 bg-black transition-opacity lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      <button
        onClick={isEditMode ? undefined : () => setIsOpen(!isOpen)}
        className={cn(
          'fixed top-6 z-50 rounded-lg bg-neutral-800 p-2 text-white lg:hidden',
          isOpen ? 'right-[270px]' : 'right-4',
          isEditMode && 'pointer-events-none opacity-50'
        )}
        disabled={isEditMode}
      >
        {isOpen ? (
          <XMarkIcon className="h-6 w-6" />
        ) : (
          <Bars3Icon className="h-6 w-6" />
        )}
      </button>

      <div
        className={cn(
          'fixed top-0 right-0 flex h-screen flex-col overflow-hidden bg-neutral-900 text-white transition-all duration-200 ease-out',
          // Mobile behavior
          isMobile && 'z-40 w-64',
          isMobile && !isOpen && 'translate-x-full',
          // Desktop behavior
          !isMobile && 'z-30',
          !isMobile && shouldBeMini && !isExpanded && 'w-16',
          !isMobile && shouldBeMini && isExpanded && 'z-50 w-64 shadow-2xl',
          !isMobile && !shouldBeMini && 'w-64'
        )}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <Link
          to={isEditMode ? '#' : '/'}
          className={cn(
            'flex h-20 items-center px-6 transition-all duration-200',
            isEditMode && 'pointer-events-none opacity-50',
            shouldBeMini && !isExpanded && 'justify-center px-4'
          )}
          onClick={(e) => isEditMode && e.preventDefault()}
        >
          <img
            src="/images/icons/logo.svg"
            alt="Logo"
            width={32}
            height={32}
            className="flex-shrink-0"
          />
          {(!shouldBeMini || isExpanded) && (
            <h1 className="mr-3 text-xl font-bold whitespace-nowrap text-white transition-all duration-200">
              مرکز هشدار ایران
            </h1>
          )}
        </Link>

        <nav className="flex-1 p-3">
          <ul className="space-y-1">
            {menuItems.map((item) => {
              const isActive = isLinkActive(item.href);
              return (
                <li key={item.name}>
                  <Link
                    to={isEditMode ? '#' : item.href || '#'}
                    className={cn(
                      'group relative flex items-center rounded-xl p-3 transition-all duration-200 hover:scale-[1.02]',
                      isEditMode && 'pointer-events-none opacity-50',
                      shouldBeMini && !isExpanded && 'justify-center',
                      isActive
                        ? shouldBeMini && !isExpanded
                          ? 'text-gray-300' // در حالت mini فقط متن عادی
                          : 'bg-primary-500 text-white shadow-lg' // در حالت عادی پس‌زمینه
                        : 'text-gray-300 hover:bg-neutral-800 hover:text-white'
                    )}
                    onClick={(e) => isEditMode && e.preventDefault()}
                    title={shouldBeMini && !isExpanded ? item.name : undefined}
                  >
                    <item.icon
                      className={cn(
                        'h-5 w-5 flex-shrink-0 transition-all duration-200',
                        isActive &&
                          (shouldBeMini && !isExpanded
                            ? 'text-gray-300'
                            : 'text-white')
                      )}
                    />
                    {(!shouldBeMini || isExpanded) && (
                      <span className="mr-3 text-sm font-medium whitespace-nowrap transition-all duration-200">
                        {item.name}
                      </span>
                    )}

                    {/* Active indicator */}
                    {isActive && (
                      <div
                        className={cn(
                          'bg-primary-500 absolute top-1/2 left-0 -translate-y-1/2',
                          shouldBeMini && !isExpanded
                            ? 'h-6 w-1' // در حالت mini: خط ساده بدون radius
                            : 'h-6 w-1 rounded-r-full' // در حالت عادی: با radius
                        )}
                      ></div>
                    )}

                    {/* Tooltip for mini mode */}
                    {shouldBeMini && !isExpanded && (
                      <div className="pointer-events-none absolute top-1/2 right-full z-50 mr-3 -translate-y-1/2 rounded-lg bg-neutral-800 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-xl transition-all duration-200 group-hover:translate-x-1 group-hover:opacity-100">
                        {item.name}
                        <div className="absolute top-1/2 left-full h-0 w-0 -translate-y-1/2 border-4 border-transparent border-r-neutral-800"></div>
                      </div>
                    )}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        <div className="border-t border-gray-800 p-3">
          {/* Toggle Mini Mode Button - only show on desktop and when not forced */}
          {!isMobile && !forceMiniMode && (
            <button
              onClick={isEditMode ? undefined : onToggleMiniMode}
              className={cn(
                'group relative flex w-full items-center rounded-xl p-3 text-gray-400 transition-all duration-200 hover:scale-[1.02] hover:bg-neutral-800 hover:text-white',
                isEditMode && 'pointer-events-none opacity-50',
                shouldBeMini && !isExpanded && 'justify-center'
              )}
              title={
                shouldBeMini && !isExpanded
                  ? isMiniMode
                    ? 'حالت عادی'
                    : 'حالت کوچک'
                  : undefined
              }
            >
              <div className="flex h-5 w-5 items-center justify-center">
                {isMiniMode ? (
                  <ArrowsPointingInIcon className="h-5 w-5 flex-shrink-0 transition-transform duration-200 group-hover:translate-x-0.5" />
                ) : (
                  <ArrowsPointingOutIcon className="h-5 w-5 flex-shrink-0 transition-transform duration-200 group-hover:-translate-x-0.5" />
                )}
              </div>
              {(!shouldBeMini || isExpanded) && (
                <span className="mr-3 text-sm font-medium whitespace-nowrap transition-all duration-200">
                  {isMiniMode ? 'حالت عادی' : 'کوچک کردن منو'}
                </span>
              )}

              {/* Tooltip for mini mode */}
              {shouldBeMini && !isExpanded && (
                <div className="pointer-events-none absolute top-1/2 right-full z-50 mr-3 -translate-y-1/2 rounded-lg bg-neutral-800 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-xl transition-all duration-200 group-hover:translate-x-1 group-hover:opacity-100">
                  {isMiniMode ? 'حالت عادی' : 'حالت کوچک'}
                  <div className="absolute top-1/2 left-full h-0 w-0 -translate-y-1/2 border-4 border-transparent border-r-neutral-800"></div>
                </div>
              )}
            </button>
          )}
        </div>
      </div>
    </>
  );
}
