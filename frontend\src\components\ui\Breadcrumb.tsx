import { cn } from '@/utils/utlis';
import { Link } from 'react-router-dom';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
  disabled?: boolean;
}

const Breadcrumb = ({
  items,
  className,
  disabled = false,
}: BreadcrumbProps) => {
  return (
    <nav className={cn('flex items-center gap-4', className)}>
      {items.map((item, index) => {
        const isLast = index === items.length - 1;
        return (
          <div key={index} className="flex items-center gap-2">
            {item.href && !isLast && !disabled ? (
              <Link
                to={item.href}
                className="text-[16px] text-neutral-400 transition-colors hover:text-white"
              >
                {item.label}
              </Link>
            ) : (
              <span
                className={cn(
                  'text-[16px] transition-colors',
                  disabled
                    ? 'text-neutral-600'
                    : isLast
                      ? 'text-primary-500'
                      : 'text-neutral-400'
                )}
              >
                {item.label}
              </span>
            )}
            {!isLast && (
              <svg
                width="14"
                height="13"
                viewBox="0 0 14 13"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3 text-gray-400"
              >
                <path
                  d="M0.491211 0.745605V12.2545H13.6442L0.491211 0.745605Z"
                  fill="currentColor"
                />
              </svg>
            )}
          </div>
        );
      })}
    </nav>
  );
};

export default Breadcrumb;
