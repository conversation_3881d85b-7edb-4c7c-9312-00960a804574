import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/utils/utlis';

interface CollapsibleProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  icon?: React.ReactNode;
  disabled?: boolean;
}

const Collapsible: React.FC<CollapsibleProps> = ({
  title,
  children,
  defaultOpen = true,
  className,
  headerClassName,
  contentClassName,
  icon,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const toggleOpen = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className={cn('w-full', className)}>
      {/* Header - Simple text and arrow */}
      <div
        className={cn(
          'flex cursor-pointer items-center justify-between py-2',
          disabled && 'cursor-not-allowed opacity-50',
          headerClassName
        )}
        onClick={toggleOpen}
      >
        <h3 className="text-lg font-medium text-white">
          {title}
        </h3>

        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2, ease: 'easeInOut' }}
          className="text-gray-400"
        >
          <ChevronDown size={20} />
        </motion.div>
      </div>

      {/* Content */}
      <AnimatePresence initial={false}>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{
              height: 'auto',
              opacity: 1,
              transition: {
                height: {
                  type: 'spring',
                  stiffness: 300,
                  damping: 30,
                  mass: 0.8,
                },
                opacity: {
                  duration: 0.2,
                  delay: 0.1,
                }
              }
            }}
            exit={{
              height: 0,
              opacity: 0,
              transition: {
                height: {
                  type: 'spring',
                  stiffness: 400,
                  damping: 40,
                  mass: 0.8,
                },
                opacity: {
                  duration: 0.15,
                }
              }
            }}
            className="overflow-hidden"
          >
            <div className={cn('mt-4', contentClassName)}>
              {children}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Collapsible;
