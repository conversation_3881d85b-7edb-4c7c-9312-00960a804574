import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowCounterClockwiseIcon,
  PencilIcon,
  CornersOutIcon,
  CornersInIcon,
  ShareNetworkIcon,
  UsersIcon,
  TrashIcon,
  PushPinIcon,
  PushPinSlashIcon,
  TrashSimpleIcon,
  XIcon,
  SquaresFourIcon,
  FloppyDiskBackIcon,
} from '@phosphor-icons/react';
import { PlusIcon } from 'lucide-react';
import { cn } from '@/utils/utlis';
import { ConfirmModal } from '@/components/ui/ConfirmModal';
import Button from '@/components/ui/Button';
import { deleteDashboard } from '@/services/dashboardService';

interface DashboardToolbarProps {
  onUpdateReports?: () => void;
  onEditDashboard?: () => void;
  onViewMode?: () => void;
  onShare?: () => void;
  onUserAccess?: () => void;
  onDelete?: () => void;
  className?: string;
  dashboardId?: string | number;
  onDeleted?: () => void;
  isFullscreen?: boolean;
  onFullscreenChange?: (isFullscreen: boolean) => void;
  isEditMode?: boolean;
  // New props for layout management and add report
  onAddReport?: () => void;
  onLayoutSelectChange?: (value: string) => void;
  pendingChanges?: boolean;
}

const DashboardToolbar: React.FC<DashboardToolbarProps> = ({
  onUpdateReports,
  onEditDashboard,
  onViewMode,
  onShare,
  onUserAccess,
  onDelete,
  className,
  dashboardId,
  onDeleted,
  isFullscreen = false,
  onFullscreenChange,
  isEditMode = false,
  onAddReport,
  onLayoutSelectChange,
  pendingChanges = false,
}) => {
  const navigate = useNavigate();
  const [isPinned, setIsPinned] = useState(true);
  const [isVisible, setIsVisible] = useState(true);
  const [hideTimeout, setHideTimeout] = useState<NodeJS.Timeout | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const savedPinnedState = localStorage.getItem('dashboardToolbarPinned');
    if (savedPinnedState !== null) {
      const pinned = JSON.parse(savedPinnedState);
      setIsPinned(pinned);
      setIsVisible(pinned);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('dashboardToolbarPinned', JSON.stringify(isPinned));
  }, [isPinned]);

  // Keep toolbar visible in edit mode
  useEffect(() => {
    if (isEditMode) {
      setIsVisible(true);
      if (hideTimeout) {
        clearTimeout(hideTimeout);
        setHideTimeout(null);
      }
    }
  }, [isEditMode, hideTimeout]);

  useEffect(() => {
    return () => {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }
    };
  }, [hideTimeout]);

  const handlePinToggle = () => {
    const newPinnedState = !isPinned;
    setIsPinned(newPinnedState);

    if (newPinnedState) {
      setIsVisible(true);
    } else {
      // In edit mode, keep toolbar visible even when unpinned
      setIsVisible(isEditMode ? true : false);
    }

    if (hideTimeout) {
      clearTimeout(hideTimeout);
      setHideTimeout(null);
    }
  };

  const handleMouseEnterHoverArea = () => {
    if (!isPinned) {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
        setHideTimeout(null);
      }
      setIsVisible(true);
    }
  };

  const handleMouseEnterToolbar = () => {
    if (!isPinned) {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
        setHideTimeout(null);
      }
      setIsVisible(true);
    }
  };

  const handleMouseLeave = () => {
    if (!isPinned && !isEditMode) {
      // Add a small delay before hiding (only if not in edit mode)
      const timeout = setTimeout(() => {
        setIsVisible(false);
        setHideTimeout(null);
      }, 300);
      setHideTimeout(timeout);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!dashboardId) return;

    try {
      setIsDeleting(true);
      await deleteDashboard(dashboardId);
      onDeleted?.();
      setShowDeleteModal(false);
    } catch (error) {
      console.error('Error deleting dashboard:', error);
      // You could add a toast notification here
      alert(error instanceof Error ? error.message : 'خطا در حذف داشبورد');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleViewModeClick = async () => {
    try {
      if (isFullscreen) {
        // Exit fullscreen
        if (document.fullscreenElement) {
          await document.exitFullscreen();
        }
        onFullscreenChange?.(false);
      } else {
        // Enter fullscreen
        await document.documentElement.requestFullscreen();
        onFullscreenChange?.(true);
      }
    } catch (error) {
      console.error('Error toggling fullscreen:', error);
    }

    // Also call the original onViewMode if provided
    onViewMode?.();
  };

  const handleEditDashboard = () => {
    if (dashboardId) {
      navigate(`/dashboard/${dashboardId}/edit`);
    }
    // Also call the original onEditDashboard if provided
    onEditDashboard?.();
  };

  const toolbarItems = [
    {
      id: 'update-reports',
      label: 'به‌روزرسانی گزارش‌ها',
      icon: ArrowCounterClockwiseIcon,
      onClick: onUpdateReports,
      disabled: false, // فعال کردن دکمه
    },
    {
      id: 'edit-dashboard',
      label: 'ویرایش داشبورد',
      icon: PencilIcon,
      onClick: handleEditDashboard,
      disabled: isEditMode,
    },
    {
      id: 'view-mode',
      label: isFullscreen ? 'خروج از تمام صفحه' : 'حالت تمام صفحه',
      icon: isFullscreen ? CornersInIcon : CornersOutIcon,
      onClick: handleViewModeClick,
      disabled: isEditMode,
    },
    {
      id: 'share',
      label: 'اشتراک گذاری',
      icon: ShareNetworkIcon,
      onClick: onShare,
      disabled: isEditMode,
    },
    {
      id: 'user-access',
      label: 'دسترسی به کاربران',
      icon: UsersIcon,
      onClick: onUserAccess,
      variant: 'secondary' as const,
      disabled: isEditMode,
    },
    {
      id: 'delete',
      label: 'حذف داشبورد',
      icon: TrashIcon,
      onClick: dashboardId ? handleDeleteClick : onDelete,
      disabled: isEditMode,
    },
    // Layout button - Add to main toolbar
    {
      id: 'layout',
      label: 'تنظیم چیدمان',
      icon: SquaresFourIcon,
      onClick: onLayoutSelectChange
        ? () => onLayoutSelectChange('edit')
        : undefined,
      disabled: isEditMode,
      show: !!onLayoutSelectChange,
    },

    {
      id: 'pin',
      label: isPinned ? 'آزاد کردن نوار ابزار' : 'پین کردن نوار ابزار',
      icon: isPinned ? PushPinIcon : PushPinSlashIcon,
      onClick: handlePinToggle,
      isActive: isPinned,
      disabled: false, // Pin should always work
    },
  ];

  return (
    <div className="relative">
      {!isPinned && !isVisible && (
        <div
          className="absolute top-0 right-0 left-0 z-50 h-2 bg-transparent"
          onMouseEnter={handleMouseEnterHoverArea}
        />
      )}

      <div
        className={cn(
          'sticky top-0 z-40 overflow-hidden transition-all duration-400 ease-in-out',
          !isPinned && !isVisible ? 'max-h-0 opacity-0' : 'max-h-20 opacity-100'
        )}
        onMouseEnter={handleMouseEnterToolbar}
        onMouseLeave={handleMouseLeave}
      >
        <div
          className={cn(
            'flex items-center justify-between border-b border-neutral-700 bg-neutral-900 px-8 py-4 transition-all duration-400 ease-in-out',
            !isPinned && !isVisible
              ? '-translate-y-2 scale-98 transform opacity-0'
              : 'translate-y-0 scale-100 transform opacity-100',
            className
          )}
        >
          {/* Left side - Main toolbar items */}
          <div className="flex items-center">
            {toolbarItems.slice(0, -1).map((item, index) => {
              const IconComponent = item.icon;

              // Skip items that have show property set to false
              if ('show' in item && !item.show) {
                return null;
              }

              // If this is the layout button and we're in edit mode, show save/cancel instead
              if (item.id === 'layout' && isEditMode) {
                return (
                  <React.Fragment key="edit-controls">
                    <button
                      onClick={() => onLayoutSelectChange?.('save')}
                      disabled={!pendingChanges}
                      className={cn(
                        'flex cursor-pointer items-center gap-2 border-r border-gray-300/40 px-4 py-2 text-sm font-medium transition-all',
                        !pendingChanges
                          ? 'pointer-events-none cursor-not-allowed text-gray-300 opacity-30'
                          : 'text-primary-500 hover:text-primary-400'
                      )}
                    >
                      <FloppyDiskBackIcon className="h-4 w-4" />
                      <span>ذخیره</span>
                    </button>
                    <button
                      onClick={() => onLayoutSelectChange?.('cancel')}
                      className="flex cursor-pointer items-center gap-2 border-r border-gray-300/40 px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:text-white"
                    >
                      <XIcon className="h-4 w-4" />
                      <span>لغو تغییرات</span>
                    </button>
                  </React.Fragment>
                );
              }

              return (
                <button
                  key={item.id}
                  onClick={item.disabled ? undefined : item.onClick}
                  disabled={item.disabled}
                  className={cn(
                    'flex cursor-pointer items-center gap-2 px-4 py-2 text-sm font-medium text-gray-300 transition-all',
                    !isPinned && !isVisible
                      ? 'scale-95 opacity-0'
                      : 'scale-100 opacity-100',
                    index > 0 && 'border-r border-gray-300/40',
                    item.disabled &&
                      'pointer-events-none cursor-not-allowed opacity-30'
                  )}
                  style={{
                    transitionDuration:
                      !isPinned && !isVisible ? '250ms' : '300ms',
                    transitionDelay:
                      !isPinned && !isVisible
                        ? `${(toolbarItems.length - index - 1) * 25}ms`
                        : `${index * 50}ms`,
                  }}
                >
                  <IconComponent className="h-4 w-4" />
                  <span>{item.label}</span>
                </button>
              );
            })}
          </div>

          {/* Right side - Add Report button and Pin */}
          <div className="flex items-center gap-4">
            {/* Add Report Button - Always show */}
            {onAddReport && (
              <Button
                variant="primary"
                icon={<PlusIcon className="h-4 w-4" />}
                onClick={onAddReport}
                className={cn(
                  'flex items-center gap-2',
                  isEditMode && 'pointer-events-none opacity-30'
                )}
                disabled={isEditMode}
              >
                افزودن گزارش
              </Button>
            )}

            {/* Pin button - Icon only */}
            {(() => {
              const pinItem = toolbarItems[toolbarItems.length - 1];
              const IconComponent = pinItem.icon;
              return (
                <button
                  key={pinItem.id}
                  onClick={pinItem.onClick}
                  disabled={pinItem.disabled}
                  title={pinItem.label}
                  className={cn(
                    'flex cursor-pointer items-center justify-center p-2 text-gray-300 transition-all hover:text-white',
                    !isPinned && !isVisible
                      ? 'scale-95 opacity-0'
                      : 'scale-100 opacity-100',
                    pinItem.disabled && 'cursor-not-allowed opacity-50'
                  )}
                  style={{
                    transitionDuration:
                      !isPinned && !isVisible ? '250ms' : '300ms',
                    transitionDelay:
                      !isPinned && !isVisible
                        ? `0ms`
                        : `${(toolbarItems.length - 1) * 50}ms`,
                  }}
                >
                  <IconComponent className="h-4 w-4" />
                </button>
              );
            })()}
          </div>
        </div>
      </div>

      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteConfirm}
        titleIcon={<TrashSimpleIcon size={32} weight="bold" />}
        confirmIcon={<TrashSimpleIcon className="h-4 w-4" weight="bold" />}
        variant="danger"
        title="آیا از حذف داشبورد اطمینان دارید؟"
        message="با حذف داشبورد امکان بازیابی و دسترسی مجدد به آن وجود نخواهد داشت."
        confirmText={isDeleting ? 'در حال حذف...' : 'حذف داشبورد'}
        cancelText="انصراف"
      />
    </div>
  );
};

export default DashboardToolbar;
