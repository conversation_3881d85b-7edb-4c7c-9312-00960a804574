import { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/utils/utlis';

export interface LayoutSelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface LayoutSelectProps {
  options: LayoutSelectOption[];
  onChange: (value: string) => void;
  className?: string;
  isEditMode?: boolean;
}

const LayoutSelect = ({ options, onChange, className, isEditMode = false }: LayoutSelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSelect = (optionValue: string) => {
    const option = options.find((opt) => opt.value === optionValue);
    if (!option?.disabled) {
      onChange(optionValue);
      setIsOpen(false);
    }
  };

  const availableOptions = options.filter((option) => !option.disabled);

  return (
    <div className={cn('relative', className)} ref={selectRef}>
      {isEditMode && (
        <div className="absolute inset-0 -m-1 rounded-lg bg-gradient-to-r from-primary-500 to-primary-600 opacity-75 blur-sm animate-pulse"></div>
      )}
      <div
        className={cn(
          'relative flex h-10 cursor-pointer items-center justify-between rounded-md border border-[#888888] bg-[#262626] px-3 py-2 text-sm text-gray-200 transition-all duration-300 hover:border-gray-500',
          isOpen && 'border-blue-500',
          isEditMode && 'border-primary-500 shadow-primary-500/50 shadow-lg bg-primary-500/10 z-10'
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className={cn(
          isEditMode && 'text-primary-300 font-medium'
        )}>تنظیمات چیدمان</span>
        <ChevronDown
          size={16}
          className={cn(
            'text-gray-400 transition-transform',
            isOpen && 'rotate-180',
            isEditMode && 'text-primary-400'
          )}
        />
      </div>

      {isOpen && availableOptions.length > 0 && (
        <div className="absolute top-full right-0 left-0 z-50 mt-1 overflow-hidden rounded-md bg-[#3b3b3b] shadow-lg">
          {availableOptions.map((option) => (
            <div
              key={option.value}
              className="cursor-pointer px-3 py-2 text-sm text-gray-200 transition-colors hover:bg-[#323232]"
              onClick={() => handleSelect(option.value)}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default LayoutSelect;
