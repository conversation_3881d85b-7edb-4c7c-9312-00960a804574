import React, { useEffect, useState } from 'react';
import { Edit3, <PERSON><PERSON>ir<PERSON>, Sliders, X, CheckLine } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import TextInput from '@/components/ui/TextInput';
import TagInput from '@/components/ui/TagInput';
import Button from '@/components/ui/Button';

interface QueryBuilderProps {
  isOpen: boolean;
  onClose: () => void;
  onApplyQuery: (query: string) => void;
  initialQuery?: string;
}

const QueryBuilder: React.FC<QueryBuilderProps> = ({
  isOpen,
  onClose,
  onApplyQuery,
  initialQuery = '',
}) => {
  const [andTerms, setAndTerms] = useState<string[]>([]);
  const [orTerms, setOrTerms] = useState<string[]>([]);
  const [notTerms, setNotTerms] = useState<string[]>([]);
  const [finalQuery, setF<PERSON>Query] = useState<string>('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editedQuery, setEditedQuery] = useState<string>('');

  const generateQueryString = (
    andArr: string[],
    orArr: string[],
    notArr: string[]
  ) => {
    const andClause = andArr.filter(Boolean).join(' AND ');

    let orClause = orArr.filter(Boolean).join(' OR ');
    if (orClause && orArr.length > 1) {
      orClause = `(${orClause})`;
    }

    const notClause = notArr
      .filter(Boolean)
      .map((word) => `NOT ${word}`)
      .join(' ');

    let finalQuery = [andClause, orClause].filter(Boolean).join(' AND ');

    if (notClause) {
      finalQuery += (finalQuery ? ' ' : '') + notClause;
    }

    return finalQuery.trim();
  };

  const parseQueryString = (queryString: string) => {
    const pandWords: string[] = [];
    const porWords: string[] = [];
    const pnotWords: string[] = [];

    if (!queryString) {
      return { pandWords, porWords, pnotWords };
    }

    let normalized = queryString.replace(/\s+/g, ' ').trim();

    const notRegex = /NOT\s+(.+?)(?=\s+AND\s+|$)/gi;
    let match;
    while ((match = notRegex.exec(normalized)) !== null) {
      const phrase = match[1].replace(/[()]/g, '').trim();
      pnotWords.push(phrase);
    }
    normalized = normalized.replace(notRegex, '').trim();

    const andChunks = normalized
      .split(/\s+AND\s+/i)
      .map((c) => c.trim())
      .filter(Boolean);

    andChunks.forEach((chunk) => {
      chunk = chunk.trim();

      const startsWithParen = chunk.startsWith('(');
      const endsWithParen = chunk.endsWith(')');

      if (startsWithParen && endsWithParen) {
        chunk = chunk.slice(1, -1).trim();
      }

      if (/\s+OR\s+/i.test(chunk)) {
        const ors = chunk.split(/\s+OR\s+/i).map((w) => w.trim());
        porWords.push(...ors);
      } else {
        chunk = chunk.replace(/[()]/g, '').trim();
        if (chunk) {
          pandWords.push(chunk);
        }
      }
    });

    return {
      pandWords,
      porWords,
      pnotWords,
    };
  };

  useEffect(() => {
    if (initialQuery) {
      const { pandWords, porWords, pnotWords } = parseQueryString(initialQuery);
      setAndTerms(pandWords);
      setOrTerms(porWords);
      setNotTerms(pnotWords);
    }
  }, [initialQuery]);

  useEffect(() => {
    const query = generateQueryString(andTerms, orTerms, notTerms);
    setFinalQuery(query);
    setEditedQuery(query);
  }, [andTerms, orTerms, notTerms]);

  const handleApply = () => {
    const queryToApply = isEditing ? editedQuery : finalQuery;
    onApplyQuery(queryToApply);
    onClose();
  };

  const handleEditToggle = () => {
    if (isEditing) {
      setFinalQuery(editedQuery);
    }
    setIsEditing(!isEditing);
  };

  return (
    <AnimatePresence mode="wait">
      {isOpen && (
        <motion.div
          key="search-drawer"
          className="fixed inset-0 z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          {/* Animated backdrop */}
          <motion.div
            className="absolute inset-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            onClick={onClose}
          />

          {/* Animated drawer - positioned on LEFT side, slides from LEFT to RIGHT */}
          <motion.div
            className="border-primary-500 absolute top-0 left-0 flex h-full w-[400px] flex-col border-r bg-neutral-900 shadow-2xl"
            initial={{ x: '-100%' }}
            animate={{
              x: 0,
              transition: {
                type: 'spring',
                stiffness: 300,
                damping: 30,
                mass: 0.8,
              },
            }}
            exit={{
              x: '-100%',
              transition: {
                type: 'spring',
                stiffness: 400,
                damping: 40,
                mass: 0.8,
              },
            }}
          >
            {/* Glow effect */}
            <div className="absolute inset-0" />

            {/* Content */}
            <div className="relative z-10 flex flex-1 flex-col">
              <div className="[&::-webkit-scrollbar-thumb]:bg-primary-500/30 flex-1 overflow-y-auto p-4 [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-transparent">
                {/* Header */}
                <motion.div
                  className="flex items-center justify-between"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2, duration: 0.4 }}
                >
                  <motion.button
                    onClick={onClose}
                    className="group cursor-pointer rounded-lg p-2 text-stone-400 transition-all duration-200 hover:bg-neutral-800/50 hover:text-white"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <X className="h-6 w-6 transition-transform group-hover:rotate-90" />
                  </motion.button>
                  <div className="flex items-center gap-2">
                    <span className="text-primary-400 text-lg font-medium">
                      راهنما
                    </span>
                    <HelpCircle className="text-primary-400 h-5 w-5" />
                  </div>
                </motion.div>

                {/* Form inputs with staggered animations */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.4 }}
                >
                  <TagInput
                    label="دقیقا شامل این کلمات باشد"
                    value={andTerms}
                    onChange={setAndTerms}
                    placeholder="کلمه مورد نظر را بنویسید"
                    className="mt-8"
                  />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4, duration: 0.4 }}
                >
                  <TagInput
                    label="می‌تواند حداقل یکی از این کلمات را داشته باشد"
                    value={orTerms}
                    onChange={setOrTerms}
                    placeholder="کلمه مورد نظر را بنویسید"
                    className="mt-8"
                  />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.4 }}
                >
                  <TagInput
                    label="هیچ کدام از این کلمات نباشد"
                    value={notTerms}
                    onChange={setNotTerms}
                    placeholder="کلمه مورد نظر را بنویسید"
                    className="mt-8"
                  />
                </motion.div>
              </div>

              {/* Footer */}
              <motion.div
                className="border-t border-neutral-700/50 bg-gradient-to-r from-neutral-800/50 to-neutral-700/30 p-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.4 }}
              >
                <TextInput
                  label="کوئری ایجاد شده"
                  disabled={!isEditing}
                  appendIcon={isEditing ? <CheckLine /> : <Edit3 />}
                  onAppendClick={handleEditToggle}
                  value={editedQuery}
                  onChange={(e) => setEditedQuery(e.target.value)}
                />

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant="primary"
                    className="shadow-primary-500/20 mt-6 h-[48px] w-full text-lg shadow-lg"
                    onClick={handleApply}
                  >
                    تایید
                  </Button>
                </motion.div>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

interface SearchDrawerInputProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    'value' | 'onChange'
  > {
  label?: string;
  value?: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
}

export const SearchDrawerInput: React.FC<SearchDrawerInputProps> = ({
  label,
  value = '',
  onChange,
  error,
  name,
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleApplyQuery = (query: string) => {
    // Create a synthetic event to maintain the same interface
    const syntheticEvent = {
      target: { value: query, name: name || '' },
      currentTarget: { value: query, name: name || '' },
    } as React.ChangeEvent<HTMLInputElement>;
    onChange(syntheticEvent);
  };

  return (
    <div>
      <TextInput
        label={label}
        name={name}
        value={value}
        onChange={onChange}
        error={error}
        appendIcon={<Sliders />}
        onAppendClick={() => setIsOpen(true)}
        {...props}
      />

      <QueryBuilder
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onApplyQuery={handleApplyQuery}
        initialQuery={value}
      />
    </div>
  );
};

export default SearchDrawerInput;
