import { cn } from '@/utils/utlis';
import { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { ChevronDown, X } from 'lucide-react';

export interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps {
  label: string;
  options: SelectOption[];
  value: string | string[];
  onChange: (value: string | string[]) => void;
  multiple?: boolean;
  error?: string;
  placeholder?: string;
  className?: string;
}

const Select = ({
  label,
  options,
  value,
  onChange,
  multiple = false,
  error,
  placeholder = 'انتخاب کنید',
  className,
}: SelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
  });
  const selectRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Calculate dropdown position
  const updateDropdownPosition = () => {
    if (triggerRef.current) {
      const rect = triggerRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
      });
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    const handleScroll = () => {
      if (isOpen) {
        updateDropdownPosition();
      }
    };

    const handleResize = () => {
      if (isOpen) {
        updateDropdownPosition();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleResize);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  // Update position when dropdown opens
  useEffect(() => {
    if (isOpen) {
      updateDropdownPosition();
    }
  }, [isOpen]);

  const selectedLabels = Array.isArray(value)
    ? options.filter((opt) => value.includes(opt.value)).map((opt) => opt.label)
    : options.find((opt) => opt.value === value)?.label;

  const handleSelect = (optionValue: string) => {
    if (multiple) {
      const newValue = Array.isArray(value) ? value : [];
      if (newValue.includes(optionValue)) {
        onChange(newValue.filter((v) => v !== optionValue));
      } else {
        onChange([...newValue, optionValue]);
      }
    } else {
      onChange(optionValue);
      setIsOpen(false);
    }
  };

  const removeValue = (optionValue: string) => {
    if (multiple && Array.isArray(value)) {
      onChange(value.filter((v) => v !== optionValue));
    }
  };

  return (
    <div className={cn('relative w-full', className)} ref={selectRef}>
      <label className="mb-2 block font-medium text-neutral-300">{label}</label>
      <div
        ref={triggerRef}
        className={cn(
          'relative h-[55px] w-full cursor-pointer rounded-sm border bg-[#3b3b3b] px-4 pt-3.5 pb-1 font-light text-white',
          'focus:border-primary-500 focus:ring-primary-500 focus:shadow-primary-400 border-[#3b3b3b] focus:shadow-sm focus:ring-1',
          error && 'border-red-500 focus:border-red-500 focus:ring-red-500'
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex min-h-[1.5rem] w-full flex-wrap gap-2">
          {multiple && Array.isArray(value) && value.length > 0 ? (
            value.map((v) => {
              const option = options.find((opt) => opt.value === v);
              return (
                option && (
                  <span
                    key={v}
                    className="bg-primary-500/20 text-primary-400 flex items-center gap-1 rounded px-2 py-0.5"
                  >
                    {option.label}
                    <X
                      size={14}
                      className="hover:text-primary-300 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeValue(v);
                      }}
                    />
                  </span>
                )
              );
            })
          ) : selectedLabels ? (
            <span>{selectedLabels}</span>
          ) : (
            <span className="text-stone-500">{placeholder}</span>
          )}
        </div>
        <ChevronDown
          size={16}
          className={cn(
            'absolute top-1/2 left-4 -translate-y-1/2 transform text-stone-400 transition-transform',
            isOpen && 'rotate-180'
          )}
        />
      </div>
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}

      {/* Portal dropdown */}
      {isOpen &&
        createPortal(
          <div
            ref={dropdownRef}
            className="fixed z-[9999] mt-1 max-h-64 overflow-auto rounded-sm border bg-[#3b3b3b] py-1 shadow-lg"
            style={{
              top: dropdownPosition.top,
              left: dropdownPosition.left,
              width: dropdownPosition.width,
            }}
          >
            {options.map((option) => {
              const isSelected = multiple
                ? Array.isArray(value) && value.includes(option.value)
                : value === option.value;
              return (
                <div
                  key={option.value}
                  className={cn(
                    'cursor-pointer p-3.5 text-sm text-white hover:bg-neutral-700',
                    isSelected && 'bg-primary-500/20 text-primary-400'
                  )}
                  onClick={() => handleSelect(option.value)}
                >
                  {option.label}
                </div>
              );
            })}
          </div>,
          document.body
        )}
    </div>
  );
};

export default Select;
