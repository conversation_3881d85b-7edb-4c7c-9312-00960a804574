import { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { X, Check } from 'lucide-react';
import TagInput, { SelectOption } from '@/components/ui/TagInput';

type PoliticalCategory = {
  label: string;
  prob: {
    eslahtalab: number;
    edalatkhah: number;
    ahmadinezhad: number;
    osoolgera: number;
    saltanat: number;
    monafegh: number;
    barandaz: number;
    restart: number;
  };
  support_state: string | null;
};

type Gender = {
  label: string;
  prob: {
    female: number;
    male: number;
  };
};

type Age = {
  range: string;
  label: string;
};

type TwitterProfile = {
  id: string;
  user_title: string;
  user_name: string;
  avatar: string;
  original_avatar: string | null;
  banner: string | null;
  bio: string;
  website: string | null;
  location: string | null;
  ai_location: string | null;
  birthday: string | null;
  join_date: string | null;
  tweet_count: number | null;
  following_count: number;
  follower_count: number;
  ai_summary: string | null;
  political_category: PoliticalCategory;
  gender: Gender;
  age: Age;
};

export interface User {
  id: string;
  name: string;
  handle: string;
  avatar: string;
  isVerified: boolean;
  followers: string;
  posts: string;
  location: string;
}

interface UserCardProps {
  user: User;
  isSelected: boolean;
  isPreviewed: boolean;
  onPreview: () => void;
  onToggleSelect: () => void;
}

function UserCard({
  user,
  isSelected,
  isPreviewed,
  onPreview,
  onToggleSelect,
}: UserCardProps) {
  return (
    <motion.div
      layout
      className={`relative cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 ${
        isPreviewed
          ? 'border-green-400 bg-gray-800/50'
          : isSelected
            ? 'border-blue-400 bg-gray-800/30'
            : 'border-gray-600 bg-gray-800/20 hover:border-gray-500'
      }`}
      onClick={onPreview}
    >
      {/* Checkbox */}
      <div
        className="absolute top-3 left-3 z-10"
        onClick={(e) => {
          e.stopPropagation();
          onToggleSelect();
        }}
      >
        <div
          className={`flex h-5 w-5 items-center justify-center rounded border-2 transition-colors ${
            isSelected
              ? 'border-blue-500 bg-blue-500'
              : 'border-gray-400 hover:border-gray-300'
          }`}
        >
          {isSelected && <Check size={12} className="text-white" />}
        </div>
      </div>

      {/* Avatar with cyber frame */}
      <div className="mb-3 flex flex-col items-center">
        <div className="relative">
          <div
            className={`h-16 w-16 rounded-full border-2 p-1 ${
              isPreviewed ? 'border-green-400' : 'border-cyan-400'
            }`}
          >
            <img
              src={user.avatar}
              alt={user.name}
              className="h-full w-full rounded-full object-cover"
            />
          </div>
          {user.isVerified && (
            <div className="absolute -right-1 -bottom-1 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500">
              <Check size={12} className="text-white" />
            </div>
          )}
        </div>
      </div>

      {/* User info */}
      <div className="text-center">
        <h3 className="mb-1 text-sm font-medium text-white">{user.name}</h3>
        <p className="mb-2 text-xs text-gray-400">{user.handle}</p>
        <div className="flex justify-between text-xs text-gray-500">
          <span>{user.followers}</span>
          <span>{user.posts}</span>
        </div>
      </div>
    </motion.div>
  );
}

interface UserDetailPanelProps {
  user: User | null;
}

function UserDetailPanel({ user }: UserDetailPanelProps) {
  if (!user) {
    return (
      <div className="flex flex-1 items-center justify-center rounded-lg border border-cyan-400 bg-gray-900/90 p-6 backdrop-blur-sm">
        <p className="text-center text-gray-400">
          برای مشاهده جزئیات، روی یکی از کاربران کلیک کنید
        </p>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className="flex-1 rounded-lg border border-cyan-400 bg-gray-900/90 p-6 backdrop-blur-sm"
      dir="rtl"
    >
      <div className="mb-6 text-center">
        <div className="relative mb-4 inline-block">
          <div className="h-20 w-20 rounded-full border-2 border-cyan-400 p-1">
            <img
              src={user.avatar}
              alt={user.name}
              className="h-full w-full rounded-full object-cover"
            />
          </div>
          {user.isVerified && (
            <div className="absolute -right-1 -bottom-1 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500">
              <Check size={12} className="text-white" />
            </div>
          )}
        </div>
        <h2 className="mb-1 text-lg font-bold text-white">{user.name}</h2>
        <p className="mb-4 text-sm text-cyan-400">{user.handle}</p>
      </div>

      <div className="space-y-4">
        <div className="rounded-lg bg-gray-800/50 p-4">
          <h3 className="mb-2 font-medium text-cyan-400">آمار</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">دنبال‌کننده:</span>
              <p className="font-medium text-white">{user.followers}</p>
            </div>
            <div>
              <span className="text-gray-400">پست:</span>
              <p className="font-medium text-white">{user.posts}</p>
            </div>
          </div>
        </div>

        <div className="rounded-lg bg-gray-800/50 p-4">
          <h3 className="mb-2 font-medium text-cyan-400">موقعیت</h3>
          <p className="text-sm text-white">{user.location}</p>
        </div>

        <div className="rounded-lg bg-gray-800/50 p-4">
          <h3 className="mb-2 font-medium text-cyan-400">درباره ما</h3>
          <p className="text-sm leading-relaxed text-gray-300">
            لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با
            استفاده از طراحان گرافیک است.
          </p>
        </div>
      </div>
    </motion.div>
  );
}

interface SourceInputProps {
  label?: string;
  value?: string[];
  onChange?: (sources: string[]) => void;
  placeholder?: string;
  className?: string;
  data: TwitterProfile[]; // Array of users passed from parent
  quickSelectCount?: number; // Number of users to show in quick dropdown
}

export default function SourceInput({
  label = 'انتخاب منابع',
  value = [],
  onChange,
  placeholder = 'منابع مورد نظر خود را انتخاب کنید',
  className,
  data = [],
  quickSelectCount = 3,
}: SourceInputProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
  const [previewedUser, setPreviewedUser] = useState<User | null>(null);

  const quickOptions: SelectOption[] = data
    .slice(0, quickSelectCount)
    .map((profile) => ({
      value: profile.user_name,
      label: profile.user_title,
    }));

  // Helper function to convert user_name to user_title for display
  const getUserTitleFromUserName = (userName: string): string => {
    const profile = data.find((p) => p.user_name === userName);
    return profile?.user_title || userName;
  };

  // Convert user_names to user_titles for display in TagInput
  const displayValues = value.map(getUserTitleFromUserName);

  const handleModalConfirm = () => {
    // Get the user_names of currently selected users in the modal
    const selectedUserNames = Array.from(selectedUsers)
      .map((userId) => {
        const profile = data.find((u) => u.id === userId);
        return profile?.user_name || '';
      })
      .filter(Boolean);

    // Update tags to match exactly what's selected in the modal
    onChange?.(selectedUserNames);
    setIsModalOpen(false);
    setSelectedUsers(new Set());
    setPreviewedUser(null);
  };

  // Initialize selected users when modal opens based on current tags
  const handleModalOpen = () => {
    const currentlySelectedIds = new Set<string>();

    // Find IDs of users whose user_names are in the current tags
    value.forEach((tag) => {
      const profile = data.find((p) => p.user_name === tag);
      if (profile) {
        currentlySelectedIds.add(profile.id);
      }
    });

    setSelectedUsers(currentlySelectedIds);
    setIsModalOpen(true);
  };

  const handleUserPreview = (user: User) => {
    setPreviewedUser(user);
  };

  // Convert TwitterProfile to User format for the modal
  const convertProfileToUser = (profile: TwitterProfile): User => {
    return {
      id: profile.id,
      name: profile.user_title,
      handle: `@${profile.user_name}`,
      avatar: profile.avatar,
      isVerified: false, // You can add verification logic if needed
      followers: profile.follower_count.toLocaleString(),
      posts: profile.tweet_count?.toLocaleString() || '0',
      location: profile.location || profile.ai_location || 'نامشخص',
    };
  };

  const toggleUserSelection = (userId: string) => {
    const newSelected = new Set(selectedUsers);
    if (newSelected.has(userId)) {
      newSelected.delete(userId);
    } else {
      newSelected.add(userId);
    }
    setSelectedUsers(newSelected);
  };

  // Handle TagInput onChange - convert user_titles back to user_names
  const handleTagInputChange = (displayTags: string[]) => {
    const userNames = displayTags.map((displayTag) => {
      const profile = data.find((p) => p.user_title === displayTag);
      return profile?.user_name || displayTag;
    });
    onChange?.(userNames);
  };

  return (
    <div className="w-full" dir="rtl">
      {/* Use TagInput component with select mode */}
      <TagInput
        label={label}
        value={displayValues}
        onChange={handleTagInputChange}
        mode="scroll"
        inputMode="select"
        options={quickOptions}
        placeholder={placeholder}
        className={className}
        showMoreEnabled={true}
        onShowMore={handleModalOpen}
      />

      {/* Modal */}
      <AnimatePresence>
        {isModalOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4 backdrop-blur-sm"
            onClick={() => setIsModalOpen(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="flex h-[85vh] w-[90vw] overflow-hidden rounded-xl border border-gray-700 bg-gray-900/95 shadow-2xl backdrop-blur-md"
              onClick={(e) => e.stopPropagation()}
              dir="rtl"
            >
              {/* Main content area */}
              <div className="flex flex-1 flex-col p-8">
                {/* Header */}
                <div className="mb-8 flex items-center justify-between">
                  <h2 className="text-3xl font-bold text-white">
                    انتخاب منابع
                  </h2>
                  <button
                    onClick={() => setIsModalOpen(false)}
                    className="rounded-full p-2 text-gray-400 transition-colors hover:bg-gray-800 hover:text-white"
                  >
                    <X size={28} />
                  </button>
                </div>

                {/* User grid */}
                <div className="scrollbar-thin scrollbar-track-gray-800 scrollbar-thumb-gray-600 hover:scrollbar-thumb-gray-500 mb-6 grid h-[calc(85vh-200px)] grid-cols-5 gap-4 overflow-y-auto pr-2">
                  {data.map((profile) => {
                    const user = convertProfileToUser(profile);
                    return (
                      <UserCard
                        key={profile.id}
                        user={user}
                        isSelected={selectedUsers.has(profile.id)}
                        isPreviewed={previewedUser?.id === profile.id}
                        onPreview={() => handleUserPreview(user)}
                        onToggleSelect={() => toggleUserSelection(profile.id)}
                      />
                    );
                  })}
                </div>

                {/* Footer */}
                <div className="mt-auto flex items-center justify-between border-t border-gray-700 pt-6">
                  <span className="text-lg text-gray-400">
                    {selectedUsers.size} کاربر انتخاب شده
                  </span>
                  <div className="flex gap-4">
                    <button
                      onClick={() => setIsModalOpen(false)}
                      className="rounded-lg px-6 py-3 text-gray-400 transition-colors hover:bg-gray-800 hover:text-white"
                    >
                      لغو
                    </button>
                    <button
                      onClick={handleModalConfirm}
                      disabled={selectedUsers.size === 0}
                      className="rounded-lg bg-cyan-500 px-8 py-3 font-medium text-white shadow-lg transition-colors hover:bg-cyan-600 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      تایید انتخاب
                    </button>
                  </div>
                </div>
              </div>

              {/* Right panel for user details */}
              <div className="flex w-96 flex-col border-r border-gray-700 bg-gray-900/50 p-6">
                <UserDetailPanel user={previewedUser} />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
