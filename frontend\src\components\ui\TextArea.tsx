import { cn } from '@/utils/utlis';
import { TextareaHTMLAttributes } from 'react';

interface TextAreaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  error?: string;
  className?: string;
}

const TextArea = ({ label, error, className, ...props }: TextAreaProps) => {
  return (
    <div className={cn('w-full', className)}>
      <label className="mb-2 block font-medium text-neutral-300">{label}</label>
      <textarea
        {...props}
        className={cn(
          'w-full rounded-sm border bg-[#3b3b3b] px-4 py-2.5 font-light text-white',
          'focus:border-primary-500 focus:ring-primary-500 focus:shadow-primary-400 border-[#3b3b3b] focus:shadow-sm focus:ring-1 focus:outline-none',
          'min-h-[120px] resize-y placeholder-stone-400',
          error &&
            'border-red-500 focus:border-red-500 focus:shadow-red-400 focus:ring-red-500'
        )}
      />
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  );
};

export default TextArea;
