.toggle {
  direction: rtl;
}

.slider {
  height: 5px;
  width: 100%;
  cursor: pointer;
  appearance: none;
  border-radius: 0.5rem;
  direction: rtl; /* RTL for right-to-left slider behavior */
  outline: none;
}

/* Chrome / Safari / Edge */
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #5bf7fa;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 0 0 2px rgba(91, 247, 250, 0.3);
}

/* Firefox */
.slider::-moz-range-thumb {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #5bf7fa;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.slider::-moz-range-thumb:hover {
  box-shadow: 0 0 0 2px rgba(91, 247, 250, 0.3);
}

/* Remove default track styling */
.slider::-webkit-slider-track {
  background: transparent;
  height: 5px;
  border-radius: 0.5rem;
  border: none;
}

.slider::-moz-range-track {
  background: transparent;
  border: none;
  height: 5px;
  border-radius: 0.5rem;
}

/* Ensure proper focus styling */
.slider:focus {
  outline: none;
}

.slider:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 2px rgba(91, 247, 250, 0.3);
}
