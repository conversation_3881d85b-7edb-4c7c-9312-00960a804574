import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { CornersInIcon, CameraIcon } from '@phosphor-icons/react';
import { CHART_TYPE_MAPPING, ChartType } from '@/constants/chartTypes';
import { Widget, Dashboard } from '@/types/dashboard';
import { useWidgetData } from '@/hooks/useWidgetData';
import WidgetSkeleton from '@/components/ui/WidgetSkeleton';
import html2canvas from 'html2canvas';

interface WidgetFullscreenModalProps {
  isOpen: boolean;
  onClose: () => void;
  widget: Widget | null;
  dashboard: Dashboard | null;
}

const WidgetFullscreenModal: React.FC<WidgetFullscreenModalProps> = ({
  isOpen,
  onClose,
  widget,
  dashboard,
}) => {
  if (!widget || !dashboard) {
    return null;
  }
  const chartRef = useRef<HTMLDivElement>(null);
  const { data, loading, error } = useWidgetData({
    widget: widget!,
    dashboard: dashboard!,
  });

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const handleScreenshot = async () => {
    if (!chartRef.current || !widget) return;

    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: '#1f2937', // gray-800 background
        scale: 2, // Higher quality
        useCORS: true,
        allowTaint: true,
      });

      // Create download link
      const link = document.createElement('a');
      link.download = `${widget.title}-screenshot.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Error taking screenshot:', error);
      alert('خطا در گرفتن اسکرین‌شات');
    }
  };

  if (!widget || !dashboard) {
    return null;
  }

  const ChartComponent = CHART_TYPE_MAPPING[widget.chart_type as ChartType];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/80 backdrop-blur-sm"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className="relative h-[90vh] w-[95vw] rounded-lg bg-gray-900 p-6 shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="mb-4 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-white">
                {/* {widget.title} */}
              </h2>

              <div className="flex items-center space-x-2">
                <button
                  onClick={handleScreenshot}
                  className="flex items-center space-x-2 rounded-lg bg-gray-800 px-3 py-2 text-gray-300 transition-colors hover:bg-gray-700 hover:text-white"
                >
                  <CameraIcon className="h-5 w-5" />
                  <span className="text-sm">دانلود تصویر</span>
                </button>
                <button
                  onClick={onClose}
                  className="flex items-center space-x-2 rounded-lg bg-gray-800 px-3 py-2 text-gray-300 transition-colors hover:bg-gray-700 hover:text-white"
                >
                  <CornersInIcon className="h-5 w-5" />
                  <span className="text-sm">خروج از تمام صفحه</span>
                </button>
              </div>
            </div>

            {/* Content */}
            <div ref={chartRef} className="h-[calc(100%-4rem)] w-full">
              {loading ? (
                <WidgetSkeleton className="h-full w-full" />
              ) : error ? (
                <div className="flex h-full w-full items-center justify-center rounded-lg bg-red-900/20">
                  <div className="text-center text-red-400">
                    <div className="mb-2 text-lg font-medium">
                      خطا در بارگذاری داده‌ها
                    </div>
                    <div className="text-sm opacity-80">{error}</div>
                  </div>
                </div>
              ) : !ChartComponent ? (
                <div className="flex h-full w-full items-center justify-center rounded-lg bg-gray-800/50">
                  <div className="text-center text-gray-400">
                    <div className="text-lg font-medium">
                      نوع نمودار پشتیبانی نمی‌شود
                    </div>
                    <div className="text-sm opacity-80">
                      {widget.chart_type}
                    </div>
                  </div>
                </div>
              ) : (
                <ChartComponent
                  title={widget.title}
                  className="h-full w-full"
                  {...data}
                />
              )}
            </div>

            {/* Close button (X) in top-right corner */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 rounded-full bg-gray-800/50 p-2 text-gray-400 transition-colors hover:bg-gray-700 hover:text-white"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default WidgetFullscreenModal;
