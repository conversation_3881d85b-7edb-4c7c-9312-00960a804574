import React from 'react';

interface WidgetSkeletonProps {
  className?: string;
}

const WidgetSkeleton: React.FC<WidgetSkeletonProps> = ({ className = '' }) => {
  return (
    <div className={`animate-pulse rounded-lg bg-gray-800/50 p-4 ${className}`}>
      {/* Title skeleton */}
      <div className="mb-4 h-4 w-3/4 rounded bg-gray-700/50"></div>
      
      {/* Chart area skeleton */}
      <div className="flex h-full flex-col space-y-3">
        {/* Simulated chart bars/elements */}
        <div className="flex items-end space-x-2 h-32">
          <div className="h-16 w-8 rounded bg-gray-700/30"></div>
          <div className="h-24 w-8 rounded bg-gray-700/30"></div>
          <div className="h-20 w-8 rounded bg-gray-700/30"></div>
          <div className="h-28 w-8 rounded bg-gray-700/30"></div>
          <div className="h-12 w-8 rounded bg-gray-700/30"></div>
          <div className="h-32 w-8 rounded bg-gray-700/30"></div>
        </div>
        
        {/* Legend skeleton */}
        <div className="flex space-x-4">
          <div className="flex items-center space-x-2">
            <div className="h-3 w-3 rounded bg-gray-700/40"></div>
            <div className="h-3 w-16 rounded bg-gray-700/40"></div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="h-3 w-3 rounded bg-gray-700/40"></div>
            <div className="h-3 w-20 rounded bg-gray-700/40"></div>
          </div>
        </div>
      </div>
      
      {/* Loading indicator */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="flex items-center space-x-2 text-gray-400">
          <svg
            className="h-5 w-5 animate-spin"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          <span className="text-sm">در حال بارگذاری...</span>
        </div>
      </div>
    </div>
  );
};

export default WidgetSkeleton;
