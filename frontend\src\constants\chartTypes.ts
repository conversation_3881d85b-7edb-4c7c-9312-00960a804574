import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ack<PERSON><PERSON><PERSON><PERSON><PERSON>,
  GroupedBar<PERSON>hart,
  HorizontalStackedBar<PERSON>hart,
  HorizontalGroupedBarChart,
} from '@/components/charts/BarCharts';
import {
  <PERSON><PERSON><PERSON>,
  Donut<PERSON><PERSON>,
  <PERSON>Circle<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from '@/components/charts/PieCharts';
import {
  <PERSON><PERSON>hart,
  Radar<PERSON>hart,
  Polygon<PERSON>hart,
  WindRose<PERSON>hart,
  PolarRadialBarChart,
} from '@/components/charts/LineAndRadarCharts';
import {
  WordCloudChart,
  NetworkGraph,
  IranMapChart,
  WorldMapChart,
} from '@/components/charts/SpecializedCharts';
import {
  DataTable,
  SingleValueDisplay,
  ContentList,
} from '@/components/charts/DisplayComponents';

/**
 * Mapping of chart type strings to their corresponding React components
 * This constant provides a centralized way to map chart type identifiers
 * to their actual component implementations.
 */
export const CHART_TYPE_MAPPING = {
  // Bar Charts - نمودارهای میله‌ای
  bar: SimpleBarChart,
  bar_stack: StackedBar<PERSON>hart,
  bar_comp: <PERSON>ed<PERSON><PERSON><PERSON><PERSON>,
  bar_stack_hor: <PERSON>tal<PERSON>tacked<PERSON><PERSON><PERSON><PERSON>,
  bar_stack_ver: StackedBar<PERSON>hart,
  bar_comp_hor: HorizontalGroupedBarChart,
  bar_comp_ver: GroupedBarChart,

  // Line Charts - نمودارهای خطی
  line: LineChart,

  // Display Components - کامپوننت‌های نمایشی
  table: DataTable,
  badge: SingleValueDisplay,
  list: ContentList,

  // Specialized Charts - نمودارهای تخصصی
  cloud: WordCloudChart,
  network_graph: NetworkGraph,
  map_iran: IranMapChart,
  map_world: WorldMapChart,

  // Radial and Polar Charts - نمودارهای شعاعی و قطبی
  radial: PolarRadialBarChart,

  // Pie Charts - نمودارهای دایره‌ای
  pie: PieChart,
  donut: DonutChart,
  semi_pie: SemiCircleChart,

  // Tree and Block Charts - نمودارهای درختی و بلوکی
  tree: BlockChart,

  // Radar and Spider Charts - نمودارهای راداری و عنکبوتی
  radar: RadarChart,
  spider: PolygonChart,

  // Wind Charts - نمودارهای باد
  wind: WindRoseChart,
} as const;

/**
 * Type definition for all available chart types
 */
export type ChartType = keyof typeof CHART_TYPE_MAPPING;

/**
 * Array of all available chart type keys for easy iteration
 */
export const CHART_TYPES = Object.keys(CHART_TYPE_MAPPING) as ChartType[];

/**
 * Helper function to check if a string is a valid chart type
 */
export const isValidChartType = (type: string): type is ChartType => {
  return type in CHART_TYPE_MAPPING;
};

/**
 * Helper function to get the component for a given chart type
 */
export const getChartComponent = (type: ChartType) => {
  return CHART_TYPE_MAPPING[type];
};
