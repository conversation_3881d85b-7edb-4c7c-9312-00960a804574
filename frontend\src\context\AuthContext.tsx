import React, {
  createContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
} from 'react';
import {
  User,
  login as loginApi,
  getMe,
  logoutUser,
  refreshAuthToken,
} from '@/services/auth.api';

const mode = import.meta.env.MODE;

interface AuthContextType {
  user: User | null;
  login: (credit: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export { AuthContext };

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if authentication should be skipped
  const skipAuth =   mode === 'development'
    ? import.meta.env.VITE_API_BASE_URL === 'true'
    : window.env.VITE_API_BASE_URL === 'true';

  const logout = useCallback(async () => {
    try {
      if (!skipAuth) {
        await logoutUser();
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      if (!skipAuth) {
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
      }
      setUser(null);
      setError(null);
    }
  }, [skipAuth]);

  useEffect(() => {
    const initAuth = async () => {
      if (skipAuth) {
        // Create a mock user when skipping authentication
        setUser({
          uuid: 'dev-user-123',
          last_login: new Date().toISOString(),
          avatar: '',
          username: 'developer',
          first_name: 'Dev',
          last_name: 'User',
          email: '<EMAIL>',
          phone_number: '',
          registration_date: new Date().toISOString(),
          role: 'admin',
          groups: [],
          user_permissions: [],
        });
        setIsLoading(false);
        return;
      }

      const token = localStorage.getItem('authToken');
      const refreshToken = localStorage.getItem('refreshToken');

      if (token) {
        // We have an access token, try to get user data
        try {
          const userData = await getMe();
          setUser(userData);
        } catch {
          // Access token is invalid, try to refresh if we have refresh token
          if (refreshToken) {
            try {
              const tokenResponse = await refreshAuthToken(refreshToken);
              localStorage.setItem('authToken', tokenResponse.access);

              // Update refresh token if server provided a new one
              if (tokenResponse.refresh) {
                localStorage.setItem('refreshToken', tokenResponse.refresh);
              }

              // Try to get user data with new token
              const userData = await getMe();
              setUser(userData);
            } catch {
              // Refresh failed, clear all tokens
              localStorage.removeItem('authToken');
              localStorage.removeItem('refreshToken');
            }
          } else {
            // No refresh token, clear access token
            localStorage.removeItem('authToken');
          }
        }
      } else if (refreshToken) {
        // No access token but we have refresh token, try to get new access token
        try {
          const tokenResponse = await refreshAuthToken(refreshToken);
          localStorage.setItem('authToken', tokenResponse.access);

          // Update refresh token if server provided a new one
          if (tokenResponse.refresh) {
            localStorage.setItem('refreshToken', tokenResponse.refresh);
          }

          // Try to get user data with new token
          const userData = await getMe();
          setUser(userData);
        } catch {
          // Refresh failed, clear refresh token
          localStorage.removeItem('refreshToken');
        }
      }
      // If no tokens or all attempts failed, user remains null (not authenticated)

      setIsLoading(false);
    };
    initAuth();
  }, [skipAuth]);

  // Note: Token refresh is now handled automatically by apiClient interceptor
  // when 401 errors are received, eliminating the need for periodic refresh

  const login = async (credit: string, password: string) => {
    try {
      setError(null);

      if (skipAuth) {
        // Mock login when skipping authentication
        setUser({
          uuid: 'dev-user-123',
          last_login: new Date().toISOString(),
          avatar: '',
          username: credit || 'developer',
          first_name: 'Dev',
          last_name: 'User',
          email: '<EMAIL>',
          phone_number: '',
          registration_date: new Date().toISOString(),
          role: 'admin',
          groups: [],
          user_permissions: [],
        });
        return;
      }

      const response = await loginApi(credit, password);

      if (!response.access) {
        throw new Error('No access token received from server');
      }

      if (!response.refresh) {
        throw new Error('No refresh token received from server');
      }

      const { access, refresh } = response;

      localStorage.setItem('authToken', access);
      localStorage.setItem('refreshToken', refresh);

      const userInfo = await getMe();
      setUser(userInfo);
    } catch (err) {
      console.error('Login error:', err);
      const errorMessage =
        err instanceof Error ? err.message : 'خطا در ورود به سامانه';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        logout,
        isLoading,
        isAuthenticated: !!user,
        error,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
