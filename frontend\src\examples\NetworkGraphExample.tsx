import React from 'react';
import { NetworkGraph } from '@/components/charts/SpecializedCharts';
import type { NetworkGraphData } from '@/components/charts/SpecializedCharts';

/**
 * Example component demonstrating the responsive NetworkGraph
 * This shows how the graph automatically fits to its container bounds
 */
const NetworkGraphExample: React.FC = () => {
  // Sample data for the network graph
  const sampleData: NetworkGraphData = {
    nodes: [
      { id: 'telegram', name: 'تلگرام', color: '#004748' },
      { id: 'instagram', name: 'اینستاگرام', color: '#026B6E' },
      { id: 'twitter', name: 'توییتر', color: '#048F92' },
      { id: 'youtube', name: 'یوتوب', color: '#5BF7FA' },
      { id: 'linkedin', name: 'لینکدین', color: '#9EFEFF' },
      { id: 'facebook', name: 'فیسبوک', color: '#D0FEFF' },
      { id: 'tiktok', name: 'تیک‌تاک', color: '#FFFFFF' },
    ],
    links: [
      { from: 'telegram', to: 'instagram', weight: 5 },
      { from: 'telegram', to: 'twitter', weight: 3 },
      { from: 'instagram', to: 'youtube', weight: 4 },
      { from: 'twitter', to: 'linkedin', weight: 2 },
      { from: 'youtube', to: 'linkedin', weight: 3 },
      { from: 'facebook', to: 'instagram', weight: 4 },
      { from: 'tiktok', to: 'youtube', weight: 2 },
      { from: 'linkedin', to: 'facebook', weight: 1 },
    ],
  };

  return (
    <div className="space-y-4 p-4">
      <h1 className="mb-4 text-2xl font-bold text-white">
        NetworkGraph Responsive Example
      </h1>

      {/* Small container */}
      <div className="rounded bg-gray-800 p-4">
        <h2 className="mb-2 text-lg text-white">
          Small Container (300px height)
        </h2>
        <div style={{ height: '300px', width: '100%' }}>
          <NetworkGraph
            data={sampleData}
            title="شبکه اجتماعی کوچک"
            className="border-2 border-blue-500"
          />
        </div>
      </div>

      {/* Medium container */}
      <div className="rounded bg-gray-800 p-4">
        <h2 className="mb-2 text-lg text-white">
          Medium Container (500px height)
        </h2>
        <div style={{ height: '500px', width: '100%' }}>
          <NetworkGraph
            data={sampleData}
            title="شبکه اجتماعی متوسط"
            className="border-2 border-green-500"
          />
        </div>
      </div>

      {/* Large container */}
      <div className="rounded bg-gray-800 p-4">
        <h2 className="mb-2 text-lg text-white">
          Large Container (700px height)
        </h2>
        <div style={{ height: '700px', width: '100%' }}>
          <NetworkGraph
            data={sampleData}
            title="شبکه اجتماعی بزرگ"
            className="border-2 border-red-500"
          />
        </div>
      </div>

      {/* Responsive container */}
      <div className="rounded bg-gray-800 p-4">
        <h2 className="mb-2 text-lg text-white">
          Responsive Container (50vh height)
        </h2>
        <div style={{ height: '50vh', width: '100%' }}>
          <NetworkGraph
            data={sampleData}
            title="شبکه اجتماعی ریسپانسیو"
            className="border-2 border-yellow-500"
          />
        </div>
      </div>

      <div className="mt-4 rounded bg-gray-700 p-4 text-sm text-white">
        <h3 className="mb-2 font-bold">Features:</h3>
        <ul className="list-inside list-disc space-y-1">
          <li>✅ Automatically fits to container bounds</li>
          <li>✅ Responsive to container size changes</li>
          <li>✅ Interactive force-directed layout</li>
          <li>✅ Drag nodes, zoom, and pan</li>
          <li>✅ Animated directional particles on links</li>
          <li>✅ Custom node colors and labels</li>
          <li>✅ Persian font support</li>
          <li>✅ Debounced resize handling for performance</li>
        </ul>
      </div>
    </div>
  );
};

export default NetworkGraphExample;
