import { useEffect, useRef, useCallback } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

interface UseChartResizeOptions {
  /**
   * Debounce delay in milliseconds for resize events
   * @default 100
   */
  debounceDelay?: number;
  /**
   * Whether to maintain aspect ratio when resizing
   * @default false
   */
  maintainAspectRatio?: boolean;
  /**
   * Minimum height for the chart
   * @default 200
   */
  minHeight?: number;
  /**
   * Maximum height for the chart
   * @default undefined
   */
  maxHeight?: number;
}

/**
 * Custom hook for making Highcharts responsive to container size changes
 * This hook automatically adjusts chart height to match its container
 * and handles resize events with debouncing for better performance
 */
export const useChartResize = (
  chartRef: React.RefObject<HighchartsReact.RefObject>,
  options: UseChartResizeOptions = {}
) => {
  const {
    debounceDelay = 100,
    maintainAspectRatio = false,
    minHeight = 200,
    maxHeight,
  } = options;

  const containerRef = useRef<HTMLDivElement>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout>();
  const resizeObserverRef = useRef<ResizeObserver>();

  const resizeChart = useCallback(() => {
    if (!chartRef.current?.chart || !containerRef.current) return;

    const chart = chartRef.current.chart;
    const container = containerRef.current;
    const containerRect = container.getBoundingClientRect();

    let newHeight = containerRect.height - 15;

    // Apply height constraints
    if (newHeight < minHeight) {
      newHeight = minHeight;
    }
    if (maxHeight && newHeight > maxHeight) {
      newHeight = maxHeight;
    }

    // Calculate width if maintaining aspect ratio
    let newWidth = containerRect.width - 10;
    if (maintainAspectRatio && chart.chartHeight && chart.chartWidth) {
      const aspectRatio = chart.chartWidth / chart.chartHeight;
      newWidth = newHeight * aspectRatio;
    }

    // Update chart size
    chart.setSize(newWidth, newHeight, false);
    chart.reflow();
  }, [chartRef, minHeight, maxHeight, maintainAspectRatio]);

  const debouncedResize = useCallback(() => {
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current);
    }

    resizeTimeoutRef.current = setTimeout(() => {
      resizeChart();
    }, debounceDelay);
  }, [resizeChart, debounceDelay]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Create ResizeObserver to watch container size changes
    resizeObserverRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === container) {
          debouncedResize();
        }
      }
    });

    // Start observing the container
    resizeObserverRef.current.observe(container);

    // Initial resize
    setTimeout(() => {
      resizeChart();
    }, 50);

    // Cleanup
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, [debouncedResize, resizeChart]);

  // Handle window resize as fallback
  useEffect(() => {
    const handleWindowResize = () => {
      debouncedResize();
    };

    window.addEventListener('resize', handleWindowResize);
    return () => {
      window.removeEventListener('resize', handleWindowResize);
    };
  }, [debouncedResize]);

  return {
    containerRef,
    resizeChart: resizeChart,
  };
};

export default useChartResize;
