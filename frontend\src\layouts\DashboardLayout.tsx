import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';
import { Outlet, useLocation } from 'react-router-dom';
import React, { useState, useEffect } from 'react';
import { EditModeProvider } from '@/context/EditModeContext';
import { cn } from '@/utils/utlis';

// Routes that should force mini mode
const MINI_MODE_ROUTES = ['/dashboard/widget-form'];

// Routes that should have mini mode enabled by default (but user can expand)
const DEFAULT_MINI_ROUTES = ['/dashboard'];

export const DashboardLayout: React.FC = () => {
  const location = useLocation();
  const [isMobile, setIsMobile] = useState(false);
  const [userMiniMode, setUserMiniMode] = useState(false);

  const forceMiniMode = MINI_MODE_ROUTES.some((route) =>
    location.pathname.startsWith(route)
  );

  const defaultMiniMode =
    DEFAULT_MINI_ROUTES.some((route) => location.pathname.startsWith(route)) &&
    !forceMiniMode;

  // Determine current mini mode state
  const currentMiniMode =
    forceMiniMode || (defaultMiniMode ? userMiniMode : userMiniMode);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Initialize user preference for mini mode based on route
  useEffect(() => {
    if (defaultMiniMode) {
      setUserMiniMode(false); // حالت پیش‌فرض: باز
    }
  }, [defaultMiniMode]);

  const shouldBeMini = !isMobile && currentMiniMode;

  const handleToggleMiniMode = () => {
    if (!forceMiniMode) {
      setUserMiniMode(!userMiniMode);
    }
  };

  return (
    <EditModeProvider>
      <div className="flex min-h-screen bg-neutral-800">
        <Sidebar
          isMiniMode={currentMiniMode}
          forceMiniMode={forceMiniMode}
          onToggleMiniMode={handleToggleMiniMode}
        />
        {/* Content area that adjusts to sidebar width */}
        <div
          className={cn(
            'flex w-full flex-col transition-all duration-300 ease-in-out',
            // Mobile: no margin
            'lg:mr-64',
            // Desktop mini mode: smaller margin
            shouldBeMini && 'lg:mr-16'
          )}
        >
          <Header />
          <main className="flex-1 overflow-auto bg-[#262626] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-neutral-600">
            <div className="min-h-full">
              <Outlet />
            </div>
          </main>
        </div>
      </div>
    </EditModeProvider>
  );
};
