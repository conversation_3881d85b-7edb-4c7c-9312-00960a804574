import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, useBlocker } from 'react-router-dom';
import CustomGridDashboard from '@/pages/dashboard/components/CustomGridDashboard';
import Breadcrumb from '@/components/ui/Breadcrumb';
import DashboardToolbar from '@/components/ui/DashboardToolbar';
import { ConfirmModal } from '@/components/ui/ConfirmModal';
import WidgetFullscreenModal from '@/components/ui/WidgetFullscreenModal';
import {
  getDashboardById,
  updateWidgetPositions,
  deleteWidget,
  uploadDashboardPreview,
  updateDashboardPreview,
} from '@/services/dashboardService';
import { Dashboard, Widget } from '@/types/dashboard';
import { CheckIcon } from '@heroicons/react/24/outline';
import { TrashSimpleIcon } from '@phosphor-icons/react';
import { useEditMode } from '@/context/EditModeContext';
import { captureDashboardScreenshot } from '@/utils/screenshot';

export default function Page() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { isEditMode, setIsEditMode } = useEditMode();
  const [dashboard, setDashboard] = useState<Dashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showNavigationModal, setShowNavigationModal] = useState(false);
  const [pendingChanges, setPendingChanges] = useState(false);
  const [blockedNavigation, setBlockedNavigation] = useState<{
    proceed: () => void;
    reset: () => void;
  } | null>(null);
  const [resetLayout, setResetLayout] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isUpdatingLayout, setIsUpdatingLayout] = useState(false);
  const [fullscreenWidget, setFullscreenWidget] = useState<Widget | null>(null);
  const [showWidgetFullscreen, setShowWidgetFullscreen] = useState(false);
  const [widgetToDelete, setWidgetToDelete] = useState<Widget | null>(null);
  const [showWidgetDeleteModal, setShowWidgetDeleteModal] = useState(false);
  const [isDeletingWidget, setIsDeletingWidget] = useState(false);
  const getCurrentLayoutRef = useRef<
    | (() => Record<
        string,
        { x: number; y: number; width: number; height: number }
      >)
    | null
  >(null);
  const dashboardGridRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchDashboard = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await getDashboardById(id);
        console.log('Dashboard data received:', data);
        console.log('Dashboard widgets:', data.widgets);
        setDashboard(data);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'خطا در دریافت اطلاعات داشبورد';
        setError(errorMessage);
        console.error('Error fetching dashboard:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboard();
  }, [id]);

  // Function to refetch dashboard data
  const handleRefreshDashboard = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const data = await getDashboardById(id);
      console.log('Dashboard data refreshed:', data);
      setDashboard(data);
      // Reset layout to reflect new data
      setResetLayout(true);
      setTimeout(() => setResetLayout(false), 100);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'خطا در به‌روزرسانی اطلاعات داشبورد';
      setError(errorMessage);
      console.error('Error refreshing dashboard:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle fullscreen change events (including ESC key)
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = Boolean(document.fullscreenElement);
      setIsFullscreen(isCurrentlyFullscreen);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // Block navigation when in edit mode with unsaved changes
  const blocker = useBlocker(
    ({ currentLocation, nextLocation }) =>
      isEditMode &&
      pendingChanges &&
      currentLocation.pathname !== nextLocation.pathname
  );

  // Handle browser close/refresh warning when in edit mode with unsaved changes
  // Note: Modern browsers only show their default message, custom messages are ignored for security
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isEditMode && pendingChanges) {
        // Modern browsers ignore custom messages and show their own default warning
        // This is a security feature to prevent malicious sites from trapping users
        event.preventDefault();
        event.returnValue = ''; // Empty string or any string - browser shows its own message
        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isEditMode, pendingChanges]);

  // Handle blocked navigation
  useEffect(() => {
    if (blocker.state === 'blocked') {
      setBlockedNavigation(blocker);
      setShowNavigationModal(true);
    }
  }, [blocker]);

  const handleAddReport = () => {
    if (id) {
      navigate(`/dashboard/${id}/create-report`);
    }
  };

  const handleSaveLayout = async () => {
    console.log(
      'handleSaveLayout called with id:',
      id,
      'getCurrentLayoutRef.current:',
      !!getCurrentLayoutRef.current
    );
    if (!id || !getCurrentLayoutRef.current) return;

    try {
      setIsUpdatingLayout(true);
      console.log('Starting layout save process...');

      // Get current layout from CustomGridDashboard
      const currentLayout = getCurrentLayoutRef.current();

      // Send layout to backend and wait for it to complete
      console.log('Updating widget positions...');
      await updateWidgetPositions(id, currentLayout);
      console.log('Widget positions updated successfully');

      // Exit edit mode completely to get a clean UI state for screenshot
      console.log('Exiting edit mode...');
      setIsEditMode(false); // Also ensure edit mode is turned off
      setIsUpdatingLayout(false);
      
      // Wait for edit mode to fully exit and UI to stabilize
      console.log('Waiting for edit mode to fully exit and UI to stabilize (2 seconds)...');
      await new Promise((resolve) => setTimeout(resolve, 2000));
      
      console.log('Edit mode should now be fully off, UI should be in final state');

      // Now set updating layout again for screenshot process
      setIsUpdatingLayout(true);

      // Additional wait to ensure all transitions and animations are complete
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Capture dashboard screenshot for preview
      console.log(
        'Checking dashboardGridRef.current:',
        dashboardGridRef.current
      );
      if (dashboardGridRef.current) {
        try {
          console.log('Preparing for dashboard screenshot...');
          
          // Ensure all widgets are properly positioned and rendered
          // Multiple small waits to allow for any async rendering
          for (let i = 0; i < 3; i++) {
            await new Promise((resolve) => setTimeout(resolve, 200));
            console.log(`Screenshot preparation step ${i + 1}/3`);
          }
          
          console.log('Capturing dashboard screenshot...');
          const screenshotBlob = await captureDashboardScreenshot(
            dashboardGridRef.current
          );

          console.log('Uploading screenshot...');
          const uploadResponse = await uploadDashboardPreview(screenshotBlob);

          console.log('Updating dashboard preview...');
          await updateDashboardPreview(id, uploadResponse.image);

          console.log('Dashboard preview updated successfully');
          console.log('✅ Complete: Layout saved and screenshot updated successfully');
        } catch (screenshotError) {
          console.error('Error updating dashboard preview:', screenshotError);
          console.log('⚠️ Layout saved but screenshot update failed');
          // Don't fail the layout save if screenshot fails
        }
      } else {
        console.log(
          'dashboardGridRef.current is null, cannot capture screenshot'
        );
      }

      console.log('Screenshot process completed, cleaning up...');
      
      // Exit edit mode
      setIsEditMode(false);
      setPendingChanges(false);
      setShowConfirmModal(false);
      
      console.log('Layout save process completed successfully');
    } catch (error) {
      console.error('Error saving layout:', error);

      // Show error message
    } finally {
      setIsUpdatingLayout(false);
    }
  };

  const handleConfirmSave = () => {
    handleSaveLayout();
  };

  const handleCancelEdit = () => {
    // Reset layout to original state
    setResetLayout(true);
    setTimeout(() => setResetLayout(false), 100); // Reset the flag after a short delay

    setIsEditMode(false);
    setPendingChanges(false);
    setShowConfirmModal(false);
  };

  const handleNavigationConfirm = () => {
    if (blockedNavigation) {
      // Save changes and then proceed with navigation
      handleSaveLayout().then(() => {
        setShowNavigationModal(false);
        setBlockedNavigation(null);
        blockedNavigation.proceed();
      });
    }
  };

  const handleNavigationCancel = () => {
    if (blockedNavigation) {
      // Reset layout and proceed with navigation
      setResetLayout(true);
      setTimeout(() => setResetLayout(false), 100);

      setIsEditMode(false);
      setPendingChanges(false);
      setShowNavigationModal(false);
      setBlockedNavigation(null);
      blockedNavigation.proceed();
    }
  };

  const handleNavigationStay = () => {
    // Stay on current page
    setShowNavigationModal(false);
    setBlockedNavigation(null);
    if (blockedNavigation) {
      blockedNavigation.reset();
    }
  };

  const handleLayoutChange = () => {
    setPendingChanges(true);
  };

  const handleDashboardDeleted = () => {
    // Navigate back to dashboard list after successful deletion
    navigate('/dashboard');
  };

  const handleWidgetDeleted = (widgetId: string | number) => {
    // Remove the widget from the dashboard state
    if (dashboard) {
      const updatedWidgets =
        dashboard.widgets?.filter((w) => w.id !== widgetId) || [];
      setDashboard({
        ...dashboard,
        widgets: updatedWidgets,
      });
      setResetLayout(true);
      setTimeout(() => setResetLayout(false), 100); // Reset the flag after a short delay
    }
  };

  const handleWidgetFullscreen = (widget: Widget) => {
    setFullscreenWidget(widget);
    setShowWidgetFullscreen(true);
  };

  const handleCloseWidgetFullscreen = () => {
    setShowWidgetFullscreen(false);
    setFullscreenWidget(null);
  };

  const handleShowWidgetDeleteModal = (widget: Widget) => {
    setWidgetToDelete(widget);
    setShowWidgetDeleteModal(true);
  };

  const handleWidgetDeleteConfirm = async () => {
    if (!widgetToDelete || !dashboard) return;

    try {
      setIsDeletingWidget(true);
      await deleteWidget(dashboard.id, widgetToDelete.id);
      handleWidgetDeleted(widgetToDelete.id);
      setShowWidgetDeleteModal(false);
      setWidgetToDelete(null);
    } catch (error) {
      console.error('Error deleting widget:', error);
      alert(error instanceof Error ? error.message : 'خطا در حذف ویجت');
    } finally {
      setIsDeletingWidget(false);
    }
  };

  const handleWidgetDeleteCancel = () => {
    setShowWidgetDeleteModal(false);
    setWidgetToDelete(null);
  };

  const handleFullscreenChange = (fullscreen: boolean) => {
    setIsFullscreen(fullscreen);
  };

  const handleLayoutSelectChange = (value: string) => {
    switch (value) {
      case 'edit':
        if (!isEditMode) {
          setIsEditMode(true);
          setPendingChanges(false);
        }
        break;
      case 'save':
        if (isEditMode) {
          handleSaveLayout();
        }
        break;
      case 'cancel':
        if (isEditMode) {
          handleCancelEdit();
        }
        break;
    }
  };

  const breadcrumbItems = [
    { label: 'خانه', href: '/dashboard' },
    { label: dashboard?.title || 'داشبورد' },
  ];

  if (loading) {
    return (
      <div className="min-h-full p-8">
        <div className="flex justify-center">
          <div className="text-white">در حال بارگذاری...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-full p-8">
        <div className="flex justify-center">
          <div className="text-red-400">{error}</div>
        </div>
      </div>
    );
  }

  // Render fullscreen mode
  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 overflow-auto bg-[#262626]">
        <DashboardToolbar
          dashboardId={id}
          onDeleted={handleDashboardDeleted}
          isFullscreen={isFullscreen}
          onFullscreenChange={handleFullscreenChange}
          isEditMode={isEditMode}
          onAddReport={handleAddReport}
          onLayoutSelectChange={handleLayoutSelectChange}
          pendingChanges={pendingChanges}
          onUpdateReports={handleRefreshDashboard}
        />

        <div className="p-4">
          <CustomGridDashboard
            isEditMode={isEditMode}
            onLayoutChange={handleLayoutChange}
            resetLayout={resetLayout}
            onGetCurrentLayout={getCurrentLayoutRef}
            widgets={dashboard?.widgets || []}
            dashboard={dashboard}
            onWidgetDeleted={handleWidgetDeleted}
            onFullscreen={handleWidgetFullscreen}
            onShowDeleteModal={handleShowWidgetDeleteModal}
            gridRef={dashboardGridRef}
          />
        </div>

        <ConfirmModal
          isOpen={showConfirmModal}
          onClose={handleCancelEdit}
          onConfirm={handleConfirmSave}
          variant="primary"
          title="تایید ذخیره تغییرات"
          message="آیا می‌خواهید تغییرات اعمال شده در چیدمان نمودارها را ذخیره کنید؟"
          confirmText={isUpdatingLayout ? 'در حال ذخیره...' : 'ذخیره تغییرات'}
          cancelText="انصراف"
          titleIcon={<CheckIcon className="h-8 w-8" />}
          confirmIcon={<CheckIcon className="h-4 w-4" />}
        />

        {/* Navigation confirmation modal */}
        <ConfirmModal
          isOpen={showNavigationModal}
          onClose={handleNavigationCancel}
          onConfirm={handleNavigationConfirm}
          variant="warning"
          title="تغییر صفحه"
          message="شما در حالت ویرایش چیدمان هستید و تغییرات ذخیره نشده دارید. چه کاری می‌خواهید انجام دهید؟"
          confirmText="ذخیره و ادامه"
          cancelText="ادامه بدون ذخیره"
          additionalButton={{
            text: 'ماندن در صفحه',
            onClick: handleNavigationStay,
            variant: 'secondary',
          }}
          titleIcon={<CheckIcon className="h-8 w-8" />}
          confirmIcon={<CheckIcon className="h-4 w-4" />}
        />
      </div>
    );
  }

  // Normal mode (within layout)
  return (
    <div className="min-h-full">
      <DashboardToolbar
        dashboardId={id}
        onDeleted={handleDashboardDeleted}
        isFullscreen={isFullscreen}
        onFullscreenChange={handleFullscreenChange}
        isEditMode={isEditMode}
        onAddReport={handleAddReport}
        onLayoutSelectChange={handleLayoutSelectChange}
        pendingChanges={pendingChanges}
        onUpdateReports={handleRefreshDashboard}
      />
      <div className="p-6">
        <Breadcrumb items={breadcrumbItems} disabled={isEditMode} />

        <CustomGridDashboard
          isEditMode={isEditMode}
          onLayoutChange={handleLayoutChange}
          resetLayout={resetLayout}
          onGetCurrentLayout={getCurrentLayoutRef}
          widgets={dashboard?.widgets || []}
          dashboard={dashboard}
          onWidgetDeleted={handleWidgetDeleted}
          onFullscreen={handleWidgetFullscreen}
          onShowDeleteModal={handleShowWidgetDeleteModal}
          gridRef={dashboardGridRef}
        />
      </div>

      <ConfirmModal
        isOpen={showConfirmModal}
        onClose={handleCancelEdit}
        onConfirm={handleConfirmSave}
        variant="primary"
        title="تایید ذخیره تغییرات"
        message="آیا می‌خواهید تغییرات اعمال شده در چیدمان نمودارها را ذخیره کنید؟"
        confirmText={isUpdatingLayout ? 'در حال ذخیره...' : 'ذخیره تغییرات'}
        cancelText="انصراف"
        titleIcon={<CheckIcon className="h-8 w-8" />}
        confirmIcon={<CheckIcon className="h-4 w-4" />}
      />

      {/* Navigation confirmation modal */}
      <ConfirmModal
        isOpen={showNavigationModal}
        onClose={handleNavigationCancel}
        onConfirm={handleNavigationConfirm}
        variant="warning"
        title="تغییر صفحه"
        message="شما در حالت ویرایش چیدمان هستید و تغییرات ذخیره نشده دارید. چه کاری می‌خواهید انجام دهید؟"
        confirmText="ذخیره و ادامه"
        cancelText="ادامه بدون ذخیره"
        additionalButton={{
          text: 'ماندن در صفحه',
          onClick: handleNavigationStay,
          variant: 'secondary',
        }}
        titleIcon={<CheckIcon className="h-8 w-8" />}
        confirmIcon={<CheckIcon className="h-4 w-4" />}
      />

      {/* Widget Fullscreen Modal */}
      <WidgetFullscreenModal
        isOpen={showWidgetFullscreen}
        onClose={handleCloseWidgetFullscreen}
        widget={fullscreenWidget}
        dashboard={dashboard}
      />

      {/* Widget Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showWidgetDeleteModal}
        onClose={handleWidgetDeleteCancel}
        onConfirm={handleWidgetDeleteConfirm}
        titleIcon={<TrashSimpleIcon size={32} weight="bold" />}
        confirmIcon={<TrashSimpleIcon className="h-4 w-4" weight="bold" />}
        variant="danger"
        title="آیا از حذف گزارش اطمینان دارید؟"
        message="با حذف گزارش امکان بازیابی و دسترسی مجدد به آن وجود نخواهد داشت."
        confirmText={isDeletingWidget ? 'در حال حذف...' : 'حذف گزارش'}
        cancelText="انصراف"
      />
    </div>
  );
}
