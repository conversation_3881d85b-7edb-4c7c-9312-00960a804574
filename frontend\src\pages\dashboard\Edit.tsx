import Stepper from '@/components/ui/Stepper';
import { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Breadcrumb from '@/components/ui/Breadcrumb';
import TextInput from '@/components/ui/TextInput';
import TextArea from '@/components/ui/TextArea';
import Button from '@/components/ui/Button';
import { FileText } from 'lucide-react';
import { CheckIcon } from '@phosphor-icons/react';
import { motion, AnimatePresence } from 'framer-motion';
import SearchDrawerInput from '@/components/ui/SearchDrawerInput';
import TimePeriodPicker from '@/components/ui/TimePeriodPicker';
import * as z from 'zod';
import TagInput from '@/components/ui/TagInput';
import { updateDashboard, getDashboardById } from '@/services/dashboardService';
import { CreateDashboardPayload, Dashboard } from '@/types/dashboard';
import SourceInput from '@/components/ui/SourceInput';

interface Form2 {
  params: {
    runtime: {
      gap: number;
    };
    query: {
      q: string;
      report_type?: string;
      hashtags?: string[];
      sources?: string[];
    };
  };
}

type PoliticalCategory = {
  label: string;
  prob: {
    eslahtalab: number;
    edalatkhah: number;
    ahmadinezhad: number;
    osoolgera: number;
    saltanat: number;
    monafegh: number;
    barandaz: number;
    restart: number;
  };
  support_state: string | null;
};

type Gender = {
  label: string;
  prob: {
    female: number;
    male: number;
  };
};

type Age = {
  range: string;
  label: string;
};

type TwitterProfile = {
  id: string;
  user_title: string;
  user_name: string;
  avatar: string;
  original_avatar: string | null;
  banner: string | null;
  bio: string;
  website: string | null;
  location: string | null;
  ai_location: string | null;
  birthday: string | null;
  join_date: string | null;
  tweet_count: number | null;
  following_count: number;
  follower_count: number;
  ai_summary: string | null;
  political_category: PoliticalCategory;
  gender: Gender;
  age: Age;
  similar_accounts: any;
};

// Sample source data
const sampleSources: TwitterProfile[] = [
  {
    id: '728252911675944961',
    user_title: 'ایران اینترنشنال',
    user_name: 'IranIntl',
    avatar: 'https://s3.synappse.ir/twitter/profiles/728252911675944961.jpg',
    original_avatar:
      'https://pbs.twimg.com/profile_images/1380225668428992513/midYaSKA_normal.jpg',
    banner:
      'https://pbs.twimg.com/profile_banners/728252911675944961/**********',
    bio: '‏‏‏‏‎‎‎‎#ایران_اینترنشنال تنها شبکه خبری ۲۴ ساعته فارسی‌زبان که تازه‌ترین خبرها را در سریع‌ترین زمان پوشش می‌دهد.\n📲 **************\n📧 <EMAIL>',
    website: null,
    location: null,
    ai_location: null,
    birthday: null,
    join_date: null,
    tweet_count: null,
    following_count: 58,
    follower_count: 2099784,
    ai_summary:
      'این کاربر توئیتر به نام "ایران اینترنشنال" با عنوان "ایران اینترنشنال" و نام کاربری "ایران اینترنشنال"، یک حساب کاربری فعال در زمینه اخبار و تحلیل‌های سیاسی، اقتصادی و امنیتی است. او عمدتاً در مورد مسائل داخلی و خارجی ایران، از جمله روابط ایران با سایر کشورها، تحولات سیاسی و اقتصادی، و مسائل امنیتی و دفاعی می‌نویسد. پست‌های او اغلب شامل لینک‌های خبری و تحلیل‌های chuyên sâu هستند و نشان می‌دهند که او به دنبال ارائه اطلاعات جامع و به‌روز در مورد مسائل ایران و جهان است.',
    political_category: {
      label: 'barandaz',
      prob: {
        eslahtalab: 0.03,
        edalatkhah: 0.01,
        ahmadinezhad: 0.02,
        osoolgera: 0.01,
        saltanat: 0.09,
        monafegh: 0.08,
        barandaz: 0.74,
        restart: 0.03,
      },
      support_state: 'opposite',
    },
    gender: {
      label: 'female',
      prob: {
        female: 0,
        male: 0,
      },
    },
    age: {
      range: 'unknown',
      label: 'unknown',
    },
    similar_accounts: null,
  },
];

export default function EditDashboard() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [currentStep, setCurrentStep] = useState(0);
  const [form1, setForm1] = useState({ title: '', description: '' });
  const [form2, setForm2] = useState<Form2>({
    params: { runtime: { gap: 24 * 60 * 60 * 1000 }, query: { q: '' } }, // 24 hours in milliseconds
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [updatedDashboard, setUpdatedDashboard] = useState<Dashboard | null>(
    null
  );
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  const lastGapValueRef = useRef<number>(12 * 60 * 60 * 1000);

  const steps = [
    {
      title: 'عنوان داشبورد',
    },
    {
      title: 'تنظیمات فیلتر',
    },
    {
      title: 'تایید نهایی',
    },
  ];

  const breadcrumbItems = [
    { label: 'داشبورد', href: '/dashboard' },
    { label: 'ویرایش داشبورد' },
  ];

  // Load dashboard data on component mount
  useEffect(() => {
    const loadDashboard = async () => {
      if (!id) {
        navigate('/dashboard');
        return;
      }

      try {
        setIsLoading(true);
        const dashboard = await getDashboardById(id);

        console.log('Loaded dashboard data:', dashboard);

        // Populate form1 with dashboard data
        setForm1({
          title: dashboard.title,
          description: dashboard.description || '',
        });

        // Populate form2 with dashboard params
        setForm2({
          params: {
            runtime: {
              gap: dashboard.params.runtime.gap,
            },
            query: {
              q: dashboard.params.query.q,
              hashtags: dashboard.params.query.hashtags || [],
              sources: dashboard.params.query.sources || [],
            },
          },
        });

        // Set selected sources
        setSelectedSources(dashboard.params.query.sources || []);

        // Update the ref with the loaded gap value
        lastGapValueRef.current = dashboard.params.runtime.gap;

        console.log('Form1 after loading:', { title: dashboard.title, description: dashboard.description || '' });
        console.log('Form2 after loading:', {
          params: {
            runtime: { gap: dashboard.params.runtime.gap },
            query: {
              q: dashboard.params.query.q,
              hashtags: dashboard.params.query.hashtags || [],
              sources: dashboard.params.query.sources || [],
            },
          },
        });
        
      } catch (error) {
        console.error('Error loading dashboard:', error);
        setErrors({ 
          general: error instanceof Error ? error.message : 'خطا در بارگذاری داشبورد' 
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboard();
  }, [id, navigate]);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      console.log('handleChange called:', { name, value });
      if (name === 'title' || name === 'description') {
        setForm1((prev) => {
          const newForm1 = { ...prev, [name]: value };
          console.log('Updating form1:', newForm1);
          return newForm1;
        });
        setErrors((prev) => ({ ...prev, [name]: '' }));
      } else if (name === 'searchQuery') {
        setForm2((prev) => ({
          ...prev,
          params: {
            ...prev.params,
            query: {
              ...prev.params.query,
              q: value,
            },
          },
        }));
        setErrors((prev) => ({ ...prev, [name]: '' }));
      }
    },
    []
  );

  const handleHashtagsChange = useCallback((newTags: string[]) => {
    setForm2((prev) => ({
      ...prev,
      params: {
        ...prev.params,
        query: {
          ...prev.params.query,
          hashtags: newTags,
        },
      },
    }));
  }, []);

  const handleSourcesChange = useCallback((newSources: string[]) => {
    setSelectedSources(newSources);
    setForm2((prev) => ({
      ...prev,
      params: {
        ...prev.params,
        query: {
          ...prev.params.query,
          sources: newSources,
        },
      },
    }));
  }, []);

  const handleTimePeriodChange = useCallback((gap: number) => {
    console.log('TimePeriodPicker onChange called with:', gap, 'milliseconds');
    console.log('Last gap value from ref:', lastGapValueRef.current);

    // Add a small tolerance for floating point comparison
    const tolerance = 1000; // 1 second tolerance

    // Only update if the value is actually different (with tolerance) to prevent infinite loops
    if (Math.abs(lastGapValueRef.current - gap) > tolerance) {
      console.log('Updating gap from', lastGapValueRef.current, 'to', gap);
      lastGapValueRef.current = gap;

      setForm2((prev) => ({
        ...prev,
        params: {
          ...prev.params,
          runtime: {
            ...prev.params.runtime,
            gap,
          },
        },
      }));
    } else {
      console.log('Gap value unchanged (within tolerance), skipping update');
    }
  }, []);

  const form1Schema = z.object({
    title: z
      .string()
      .min(3, { message: 'عنوان باید حداقل ۳ کاراکتر داشته باشد.' }),
    description: z.string().optional(),
  });

  const form2Schema = z.object({
    params: z.object({
      runtime: z.object({
        gap: z.number().positive({ message: 'بازه زمانی باید مثبت باشد.' }),
      }),
      query: z.object({
        q: z.string().min(2, { message: 'عبارت جستجو الزامی است.' }),
        report_type: z.string().optional(),
        hashtags: z.array(z.string()).optional(),
        sources: z.array(z.string()).optional(),
      }),
    }),
  });

  const handleUpdateDashboard = async () => {
    if (!id) return;

    setIsSubmitting(true);
    setErrors({});

    try {
      // Combine form1 and form2 data into the payload
      const payload: CreateDashboardPayload = {
        title: form1.title,
        description: form1.description.trim() || undefined,
        params: form2.params,
      };

      console.log('Updating dashboard with payload:', payload);

      // Update the dashboard
      const dashboard = await updateDashboard(id, payload);
      console.log('Dashboard updated successfully:', dashboard);
      setUpdatedDashboard(dashboard);
      setCurrentStep(2);
    } catch (error) {
      console.error('Dashboard update error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'خطا در به‌روزرسانی داشبورد';

      // Handle Zod validation errors by mapping them to form fields
      if (error instanceof z.ZodError) {
        const errorMap: Record<string, string> = {};
        error.issues.forEach((issue) => {
          const path = issue.path.join('.');
          if (path.includes('title')) {
            errorMap['title'] = issue.message;
          } else if (path.includes('description')) {
            errorMap['description'] = issue.message;
          } else if (path.includes('q')) {
            errorMap['searchQuery'] = issue.message;
          } else {
            errorMap['general'] = issue.message;
          }
        });
        setErrors(errorMap);
      } else {
        setErrors({ general: errorMessage });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNext = () => {
    if (currentStep === 0) {
      const result = form1Schema.safeParse(form1);

      if (!result.success) {
        const errorMap: Record<string, string> = {};
        result.error.issues.forEach((issue) => {
          const field = String(issue.path[0]);
          errorMap[field] = issue.message;
        });
        setErrors(errorMap);
        return;
      }

      setCurrentStep(1);
    } else if (currentStep === 1) {
      const result = form2Schema.safeParse(form2);

      if (!result.success) {
        const errorMap: Record<string, string> = {};
        result.error.issues.forEach((issue) => {
          const path = issue.path.join('.');
          if (path.includes('q')) {
            errorMap['searchQuery'] = issue.message;
          } else {
            errorMap[String(issue.path[0])] = issue.message;
          }
        });
        setErrors(errorMap);
        return;
      }

      // Instead of going to step 2, trigger dashboard update
      handleUpdateDashboard();
    }
  };

  const handleBack = useCallback(() => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  }, []);

  const handleCancel = useCallback(() => {
    navigate(`/dashboard/${id}`);
  }, [navigate, id]);

  const handleNavigateToCreate = useCallback(() => {
    navigate('/dashboard/create');
  }, [navigate]);

  const handleNavigateToDashboard = useCallback(() => {
    navigate('/dashboard');
  }, [navigate]);

  const handleNavigateToDetail = useCallback(() => {
    if (id) {
      navigate(`/dashboard/${id}`);
    }
  }, [navigate, id]);

  // Memoize the time period value to prevent unnecessary re-renders
  const timePeriodValue = useMemo(() => {
    // Update the ref when the value changes from other sources
    lastGapValueRef.current = form2.params.runtime.gap;
    return form2.params.runtime.gap;
  }, [form2.params.runtime.gap]);

  // Show loading state while fetching dashboard data
  if (isLoading) {
    return (
      <div className="min-h-full p-8">
        <Breadcrumb items={breadcrumbItems} />
        <div className="mx-auto mt-8 flex w-full max-w-7xl flex-col items-center">
          <h1 className="text-4xl font-bold text-white">ویرایش داشبورد</h1>
          <div className="my-4 h-[2px] w-1/12 bg-gray-300"></div>
          <div className="mt-8 text-center text-white">در حال بارگذاری...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-full p-8">
      <Breadcrumb items={breadcrumbItems} />
      <div className="mx-auto mt-8 flex w-full max-w-7xl flex-col items-center">
        <h1 className="text-4xl font-bold text-white">ویرایش داشبورد</h1>
        <div className="my-4 h-[2px] w-1/12 bg-gray-300"></div>

        <Stepper steps={steps} currentStep={currentStep} className="mt-8" />

        <div className="relative mt-8 w-full pb-8">
          <AnimatePresence mode="wait">
            {currentStep === 0 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="mx-auto w-full max-w-2xl space-y-6"
              >
                <TextInput
                  label="عنوان داشبورد"
                  name="title"
                  value={form1.title}
                  onChange={handleChange}
                  placeholder="عنوان داشبورد را وارد کنید"
                  error={errors.title}
                />
                <TextArea
                  label="توضیحات"
                  name="description"
                  value={form1.description}
                  onChange={handleChange}
                  placeholder="متن مورد نظر خود را بنویسید"
                  error={errors.description}
                />
                <div className="flex flex-col gap-2 sm:flex-row">
                  <div className="flex-grow"></div>
                  <Button variant="secondary" onClick={handleCancel}>
                    انصراف
                  </Button>
                  <Button
                    onClick={handleNext}
                    icon={<FileText className="h-4 w-4" />}
                    iconPosition="start"
                  >
                    ثبت و ادامه
                  </Button>
                </div>
              </motion.div>
            )}

            {currentStep === 1 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="mx-auto w-full max-w-4xl space-y-6"
              >
                <SearchDrawerInput
                  label="عبارت جستجو"
                  name="searchQuery"
                  placeholder="عبارت جستجو خود را وارد کنید"
                  value={form2.params.query.q}
                  onChange={handleChange}
                  error={errors.searchQuery}
                />

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <TagInput
                    label="هشتگ‌ها"
                    mode="scroll"
                    name="hashtags"
                    value={form2.params.query.hashtags}
                    onChange={handleHashtagsChange}
                    placeholder="هشتگ‌های مورد نظر را وارد کنید"
                  />

                  <SourceInput
                    label="انتخاب منابع"
                    value={selectedSources}
                    onChange={handleSourcesChange}
                    placeholder="منابع مورد نظر خود را انتخاب کنید"
                    data={sampleSources}
                    quickSelectCount={5}
                  />
                </div>

                <TimePeriodPicker
                  value={timePeriodValue}
                  onChange={handleTimePeriodChange}
                  label="بازه زمانی نتایج گزارش"
                />

                <div className="h-[1px] bg-neutral-700"></div>

                {errors.general && (
                  <div className="rounded-md bg-red-50 p-4">
                    <div className="text-sm text-red-700">{errors.general}</div>
                  </div>
                )}

                <div className="flex flex-col gap-2 sm:flex-row">
                  <Button
                    variant="secondary"
                    onClick={handleBack}
                    disabled={isSubmitting}
                  >
                    قبلی
                  </Button>
                  <div className="flex-grow"></div>
                  <Button
                    variant="secondary"
                    onClick={handleCancel}
                    disabled={isSubmitting}
                  >
                    انصراف
                  </Button>
                  <Button
                    onClick={handleNext}
                    icon={<FileText className="h-4 w-4" />}
                    iconPosition="start"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'در حال به‌روزرسانی...' : 'به‌روزرسانی داشبورد'}
                  </Button>
                </div>
              </motion.div>
            )}

            {currentStep === 2 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="mx-auto w-full max-w-4xl space-y-6"
              >
                <div className="flex flex-col items-center">
                  <div className="bg-primary-500 flex h-16 w-16 items-center justify-center rounded-full">
                    <CheckIcon size={40} color="#FFFFFF" />
                  </div>

                  <div className="mt-8 text-[25px] font-bold text-white">
                    داشبورد با موفقیت به‌روزرسانی شد
                  </div>

                  <div className="mt-6 text-xl font-medium text-stone-300">
                    {updatedDashboard ? (
                      <>
                        داشبورد شما با عنوان "{updatedDashboard.title}" و عبارت
                        جستجوی "{form2.params.query.q}" به‌روزرسانی شد.
                        {form2.params.query.hashtags &&
                          form2.params.query.hashtags.length > 0 && (
                            <div className="mt-2 text-lg">
                              هشتگ‌ها: {form2.params.query.hashtags.join(', ')}
                            </div>
                          )}
                        {form2.params.query.sources &&
                          form2.params.query.sources.length > 0 && (
                            <div className="mt-2 text-lg">
                              منابع: {form2.params.query.sources.join(', ')}
                            </div>
                          )}
                        {form1.description && (
                          <div className="mt-2 text-lg">
                            توضیحات: {form1.description}
                          </div>
                        )}
                      </>
                    ) : (
                      'داشبورد شما با موفقیت به‌روزرسانی شد.'
                    )}
                  </div>

                  <div className="mt-10 flex flex-row gap-4">
                    <Button
                      variant="secondary"
                      onClick={handleNavigateToCreate}
                    >
                      ایجاد داشبورد جدید
                    </Button>
                    <Button
                      variant="primary"
                      onClick={handleNavigateToDashboard}
                    >
                      بازگشت به لیست داشبوردها
                    </Button>
                    <Button
                      variant="primary"
                      onClick={handleNavigateToDetail}
                    >
                      مشاهده داشبورد
                    </Button>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
