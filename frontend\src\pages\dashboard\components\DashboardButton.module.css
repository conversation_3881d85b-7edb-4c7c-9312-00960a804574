:root {
  --color-2: #5bf7fa;
  --color-3: #262626;
  --color-4: #5bf7fa;
  --color-5: #262626;
  --color-7: #262626;
  --color-8: rgba(68 69 70 / 0.25);
  --x: 5deg;
}

.btn {
  position: relative;
  min-width: 130px;
  height: 35px;
  display: inline-block;
  border: none;
  background: none;
  cursor: pointer;
  padding: 0;
}

.inner,
.blur {
  position: absolute;
  inset: -2px;
  display: block;
  background: linear-gradient(
    var(--x),
    var(--color-2),
    var(--color-3),
    var(--color-3),
    var(--color-4)
  );
  border-radius: 1px;
}

.blur {
  filter: blur(2px);
}

.label {
  position: absolute;
  font-size: 12px;
  font-weight: 400;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-transform: uppercase;
  letter-spacing: 2px;
  border: 1px solid var(--color-7);
  border-radius: 3px;
  background: var(--color-5);
  overflow: hidden;
  color: white;
  z-index: 1;
}

.label::before {
  content: '';
  position: absolute;
  top: 0;
  left: -50%;
  width: 100%;
  height: 100%;
  background: var(--color-8);
  transform: skew(25deg);
}
