import React, { useRef } from 'react';
import clsx from 'clsx';
import styles from './DashboardButton.module.css';

type DashboardButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  text: string;
};

const DashboardButton: React.FC<DashboardButtonProps> = ({
  text,
  className,
  ...props
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLButtonElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = (e.clientX - rect.left) * 2;
    buttonRef.current?.style.setProperty('--x', `${x}deg`);
  };

  return (
    <button
      ref={buttonRef}
      onMouseMove={handleMouseMove}
      className={clsx(styles.btn, className)}
      {...props}
    >
      <i className={styles.inner}></i>
      <i className={styles.blur}></i>
      <span className={styles.label}>{text}</span>
    </button>
  );
};

export default DashboardButton;
