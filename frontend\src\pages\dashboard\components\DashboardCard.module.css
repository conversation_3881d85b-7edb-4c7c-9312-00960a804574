.menu-box {
  position: relative;
  background: linear-gradient(to bottom, #059fad, #02717a);
  color: white;
  padding: 2rem 1.5rem;
  width: 250px;
  clip-path: polygon(
    15px 0%,
    90% 0%,
    100% 10%,
    100% 85%,
    90% 100%,
    10% 100%,
    0% 85%,
    0% 15%
  );
  border: 3px solid #00f1ff;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.menu-box p {
  margin: 1rem 0;
  text-align: center;
  font-size: 1.1rem;
  cursor: pointer;
}

.menu-box p:hover {
  text-decoration: underline;
}

.menu {
  position: absolute;
  top: 16px;
  left: 16px;
  width: 130px;
  height: 138px;
  z-index: 20;
  opacity: 0;
  visibility: hidden;
  transform: scale(0.95);
  transform-origin: top left;
  transition: all 0.2s ease-out;
}

.menu.active {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

.menuContent {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  justify-content: center;
  padding: 0 12px;
}

.menu p {
  color: white;
  padding: 6px 8px;
  margin: 0;
  cursor: pointer;
  font-size: 12px;
  border-radius: 4px;
  text-align: right;
  transition: all 0.2s ease;
  white-space: nowrap;
  opacity: 0;
  transform: translateY(10px);
}

.menu.active p {
  opacity: 1;
  transform: translateY(0);
}

.menu.active p:nth-child(1) {
  transition-delay: 0.1s;
}

.menu.active p:nth-child(2) {
  transition-delay: 0.15s;
}

.menu.active p:nth-child(3) {
  transition-delay: 0.2s;
}

.menu p:hover {
  color: #05a0a3;
  background: rgba(5, 160, 163, 0.1);
}

.fullBlurOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 15;
  pointer-events: none;
}

.fullBlurOverlay.active {
  opacity: 1;
  visibility: visible;
}
