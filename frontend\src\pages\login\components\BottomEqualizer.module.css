.equalizer {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  pointer-events: none; /* Prevent interaction issues */
}

.bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  transform-origin: bottom;
  min-width: 24px;
}

.block {
  width: 100%;
}

.block:nth-child(1) {
  height: 1px;
}

.block:nth-child(2) {
  height: 2px;
}

.block:nth-child(3) {
  height: 3px;
}

.block:nth-child(4) {
  height: 4px;
}

.block:nth-child(5) {
  height: 5px;
}

.block:nth-child(6) {
  height: 6px;
}

.block:nth-child(7) {
  height: 7px;
}

.block:nth-child(8) {
  height: 8px;
}

.block:nth-child(9) {
  height: 9px;
}

.color-1 {
  background: #636363;
}

.color-2 {
  background: #3d3d3d;
}

.color-3 {
  background: #0d736f;
}

.color-4 {
  background: #fbfbfb;
}

.color-5 {
  background: #414141;
}

.color-6 {
  background: #282828;
}

.color-7 {
  background: #084a47;
}

.color-8 {
  background: #616161;
}

.bar:nth-child(1) {
  animation: bounce1 0.6s infinite alternate ease-in-out;
}

.bar:nth-child(2) {
  animation: bounce2 0.8s infinite alternate ease-in-out;
}

.bar:nth-child(3) {
  animation: bounce3 0.4s infinite alternate ease-in-out;
}

.bar:nth-child(4) {
  animation: bounce4 1.2s infinite alternate ease-in-out;
}

.bar:nth-child(5) {
  animation: bounce5 0.7s infinite alternate ease-in-out;
}

.bar:nth-child(6) {
  animation: bounce6 0.9s infinite alternate ease-in-out;
}

.bar:nth-child(7) {
  animation: bounce7 0.5s infinite alternate ease-in-out;
}

.bar:nth-child(8) {
  animation: bounce8 1s infinite alternate ease-in-out;
}

.bar:nth-child(9) {
  animation: bounce9 0.65s infinite alternate ease-in-out;
}

.bar:nth-child(10) {
  animation: bounce10 0.85s infinite alternate ease-in-out;
}

.bar:nth-child(11) {
  animation: bounce11 0.75s infinite alternate ease-in-out;
}

.bar:nth-child(12) {
  animation: bounce12 1.1s infinite alternate ease-in-out;
}

.bar:nth-child(13) {
  animation: bounce13 0.55s infinite alternate ease-in-out;
}

.bar:nth-child(14) {
  animation: bounce14 0.95s infinite alternate ease-in-out;
}

.bar:nth-child(15) {
  animation: bounce15 0.45s infinite alternate ease-in-out;
}

.bar:nth-child(16) {
  animation: bounce16 1.05s infinite alternate ease-in-out;
}

.bar:nth-child(17) {
  animation: bounce17 0.35s infinite alternate ease-in-out;
}

.bar:nth-child(18) {
  animation: bounce18 0.9s infinite alternate ease-in-out;
}

.bar:nth-child(19) {
  animation: bounce19 0.6s infinite alternate ease-in-out;
}

.bar:nth-child(20) {
  animation: bounce20 0.8s infinite alternate ease-in-out;
}

.bar:nth-child(21) {
  animation: bounce21 0.7s infinite alternate ease-in-out;
}

.bar:nth-child(22) {
  animation: bounce22 1s infinite alternate ease-in-out;
}

.bar:nth-child(23) {
  animation: bounce23 0.5s infinite alternate ease-in-out;
}

.bar:nth-child(24) {
  animation: bounce24 0.9s infinite alternate ease-in-out;
}

.bar:nth-child(25) {
  animation: bounce25 0.65s infinite alternate ease-in-out;
}

.bar:nth-child(26) {
  animation: bounce26 1.1s infinite alternate ease-in-out;
}

.bar:nth-child(27) {
  animation: bounce27 0.4s infinite alternate ease-in-out;
}

.bar:nth-child(28) {
  animation: bounce28 1.2s infinite alternate ease-in-out;
}

.bar:nth-child(29) {
  animation: bounce29 0.55s infinite alternate ease-in-out;
}

.bar:nth-child(30) {
  animation: bounce30 0.85s infinite alternate ease-in-out;
}

.bar:nth-child(31) {
  animation: bounce31 0.45s infinite alternate ease-in-out;
}

.bar:nth-child(32) {
  animation: bounce32 1.05s infinite alternate ease-in-out;
}

.bar:nth-child(33) {
  animation: bounce33 0.6s infinite alternate ease-in-out;
}

.bar:nth-child(34) {
  animation: bounce34 0.95s infinite alternate ease-in-out;
}

.bar:nth-child(35) {
  animation: bounce35 0.7s infinite alternate ease-in-out;
}

.bar:nth-child(36) {
  animation: bounce36 0.8s infinite alternate ease-in-out;
}

.bar:nth-child(37) {
  animation: bounce37 0.5s infinite alternate ease-in-out;
}

.bar:nth-child(38) {
  animation: bounce38 1.15s infinite alternate ease-in-out;
}

.bar:nth-child(39) {
  animation: bounce39 0.65s infinite alternate ease-in-out;
}

.bar:nth-child(40) {
  animation: bounce40 1s infinite alternate ease-in-out;
}

.bar:nth-child(41) {
  animation: bounce41 0.4s infinite alternate ease-in-out;
}

.bar:nth-child(42) {
  animation: bounce42 0.9s infinite alternate ease-in-out;
}

.bar:nth-child(43) {
  animation: bounce43 0.75s infinite alternate ease-in-out;
}

.bar:nth-child(44) {
  animation: bounce44 1.1s infinite alternate ease-in-out;
}

.bar:nth-child(45) {
  animation: bounce45 0.55s infinite alternate ease-in-out;
}

.bar:nth-child(46) {
  animation: bounce46 0.85s infinite alternate ease-in-out;
}

.bar:nth-child(47) {
  animation: bounce47 0.6s infinite alternate ease-in-out;
}

.bar:nth-child(48) {
  animation: bounce48 1.2s infinite alternate ease-in-out;
}

.bar:nth-child(49) {
  animation: bounce49 0.45s infinite alternate ease-in-out;
}

.bar:nth-child(50) {
  animation: bounce50 0.95s infinite alternate ease-in-out;
}

.bar:nth-child(51) {
  animation: bounce51 0.7s infinite alternate ease-in-out;
}

.bar:nth-child(52) {
  animation: bounce52 0.8s infinite alternate ease-in-out;
}

.bar:nth-child(53) {
  animation: bounce53 0.6s infinite alternate ease-in-out;
}

.bar:nth-child(54) {
  animation: bounce54 1s infinite alternate ease-in-out;
}

.bar:nth-child(55) {
  animation: bounce55 0.5s infinite alternate ease-in-out;
}

.bar:nth-child(56) {
  animation: bounce56 0.9s infinite alternate ease-in-out;
}

.bar:nth-child(57) {
  animation: bounce57 0.65s infinite alternate ease-in-out;
}

.bar:nth-child(58) {
  animation: bounce58 1.1s infinite alternate ease-in-out;
}

.bar:nth-child(59) {
  animation: bounce59 0.75s infinite alternate ease-in-out;
}

.bar:nth-child(60) {
  animation: bounce60 0.85s infinite alternate ease-in-out;
}

@keyframes bounce1 {
  0% {
    transform: scaleY(0.3);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce2 {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce3 {
  0% {
    transform: scaleY(0.8);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce4 {
  0% {
    transform: scaleY(0.2);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce5 {
  0% {
    transform: scaleY(0.6);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce6 {
  0% {
    transform: scaleY(0.5);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce7 {
  0% {
    transform: scaleY(0.7);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce8 {
  0% {
    transform: scaleY(0.3);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce9 {
  0% {
    transform: scaleY(0.5);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce10 {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce11 {
  0% {
    transform: scaleY(0.6);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce12 {
  0% {
    transform: scaleY(0.2);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce13 {
  0% {
    transform: scaleY(0.8);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce14 {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce15 {
  0% {
    transform: scaleY(0.9);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce16 {
  0% {
    transform: scaleY(0.3);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce17 {
  0% {
    transform: scaleY(0.7);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce18 {
  0% {
    transform: scaleY(0.5);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce19 {
  0% {
    transform: scaleY(0.6);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce20 {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce21 {
  0% {
    transform: scaleY(0.5);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce22 {
  0% {
    transform: scaleY(0.3);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce23 {
  0% {
    transform: scaleY(0.8);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce24 {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce25 {
  0% {
    transform: scaleY(0.6);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce26 {
  0% {
    transform: scaleY(0.2);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce27 {
  0% {
    transform: scaleY(0.9);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce28 {
  0% {
    transform: scaleY(0.3);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce29 {
  0% {
    transform: scaleY(0.7);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce30 {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce31 {
  0% {
    transform: scaleY(0.8);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce32 {
  0% {
    transform: scaleY(0.3);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce33 {
  0% {
    transform: scaleY(0.5);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce34 {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce35 {
  0% {
    transform: scaleY(0.6);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce36 {
  0% {
    transform: scaleY(0.5);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce37 {
  0% {
    transform: scaleY(0.7);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce38 {
  0% {
    transform: scaleY(0.2);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce39 {
  0% {
    transform: scaleY(0.6);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce40 {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce41 {
  0% {
    transform: scaleY(0.8);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce42 {
  0% {
    transform: scaleY(0.5);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce43 {
  0% {
    transform: scaleY(0.3);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce44 {
  0% {
    transform: scaleY(0.2);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce45 {
  0% {
    transform: scaleY(0.7);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce46 {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce47 {
  0% {
    transform: scaleY(0.6);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce48 {
  0% {
    transform: scaleY(0.3);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce49 {
  0% {
    transform: scaleY(0.8);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce50 {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce51 {
  0% {
    transform: scaleY(0.5);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce52 {
  0% {
    transform: scaleY(0.6);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce53 {
  0% {
    transform: scaleY(0.7);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce54 {
  0% {
    transform: scaleY(0.3);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce55 {
  0% {
    transform: scaleY(0.8);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce56 {
  0% {
    transform: scaleY(0.4);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce57 {
  0% {
    transform: scaleY(0.6);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce58 {
  0% {
    transform: scaleY(0.2);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce59 {
  0% {
    transform: scaleY(0.5);
  }
  100% {
    transform: scaleY(1);
  }
}

@keyframes bounce60 {
  0% {
    transform: scaleY(0.7);
  }
  100% {
    transform: scaleY(1);
  }
}
