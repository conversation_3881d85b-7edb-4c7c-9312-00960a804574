.equalizerHorizontal {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
  width: 100%;
}

.bar {
  background: #ffffff;
  border-radius: 0 2px 2px 0;
  animation: waveHorizontal 2.5s ease-in-out infinite;
  min-width: 8px;
}

@keyframes waveHorizontal {
  0%,
  100% {
    width: 8px;
  }
  50% {
    width: 100%;
  }
}

@media (prefers-reduced-motion: reduce) {
  .bar {
    animation: none;
    width: 8px;
  }
}
