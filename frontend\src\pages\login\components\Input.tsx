'use client';

import React, { useState, InputHTMLAttributes } from 'react';
import { clsx } from 'clsx';
import { Eye, EyeClosed } from 'lucide-react';

// Extend native input props
type InputProps = {
  label?: string;
  error?: string;
  prependIcon?: React.ReactNode;
} & InputHTMLAttributes<HTMLInputElement>;

export default function Input(props: InputProps) {
  const { id, label, error, type, prependIcon, ...rest } = props;
  const [eye, setEye] = useState(true);
  const [value, setValue] = useState('');

  const inputType = type === 'password' ? (eye ? 'password' : 'text') : type;

  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={id}
          className="mb-2 block text-sm font-medium text-white"
        >
          {label}
        </label>
      )}

      <div
        className={clsx(
          'flex items-center rounded-lg border bg-black/50 focus-within:ring-2',
          error
            ? 'border-red-400 ring-2 ring-red-300 focus-within:border-red-400 focus-within:ring-red-400'
            : 'focus-within:border-primary-500 focus-within:ring-primary-500 border-gray-600'
        )}
      >
        {prependIcon && (
          <span className="flex items-center pr-3 text-gray-300">
            {prependIcon}
          </span>
        )}
        <input
          id={id}
          type={inputType}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          className="block w-full bg-transparent p-2.5 text-sm text-white placeholder-gray-400 focus:outline-none"
          {...rest}
        />
        {type === 'password' && (
          <button
            type="button"
            className="flex cursor-pointer items-center pl-3 text-gray-400"
            onClick={() => setEye(!eye)}
            tabIndex={-1}
          >
            {eye ? <Eye /> : <EyeClosed />}
          </button>
        )}
      </div>

      {error && <div className="mt-2 text-sm text-red-500">{error}</div>}
    </div>
  );
}
