import React from 'react';
import {
  IranMapChart,
  WorldMapChart,
} from '@/components/charts/SpecializedCharts';

const TestMapsPage: React.FC = () => {
  const iranData = [
    { key: 'tehran', value: 85, name: 'تهران' },
    { key: 'is<PERSON>han', value: 72, name: 'اصفهان' },
    { key: 'fars', value: 68, name: 'فارس' },
    { key: 'khorasan-razavi', value: 59, name: 'خراسان رضوی' },
    { key: 'east-azerbaijan', value: 45, name: 'آذربایجان شرقی' },
  ];

  const worldData = [
    { code: 'IR', value: 100, name: 'ایران' },
    { code: 'US', value: 85, name: 'آمریکا' },
    { code: 'DE', value: 70, name: 'آلمان' },
    { code: 'FR', value: 65, name: 'فرانسه' },
    { code: 'GB', value: 60, name: 'انگلستان' },
    { code: 'JP', value: 55, name: 'ژاپن' },
    { code: 'CN', value: 90, name: 'چین' },
  ];

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="mx-auto max-w-7xl">
        <h1 className="mb-8 text-center text-3xl font-bold text-white">
          Map Charts Test - React Simple Maps
        </h1>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          {/* Iran Map */}
          <div className="rounded-lg bg-gray-800 p-6">
            <h2 className="mb-4 text-xl font-semibold text-white">
              Iran Map Chart
            </h2>
            <div className="h-96">
              <IranMapChart
                data={iranData}
                title="نقشه ایران"
                className="h-full w-full"
                showLegend={true}
              />
            </div>
          </div>

          {/* World Map */}
          <div className="rounded-lg bg-gray-800 p-6">
            <h2 className="mb-4 text-xl font-semibold text-white">
              World Map Chart
            </h2>
            <div className="h-96">
              <WorldMapChart
                data={worldData}
                title="نقشه جهان"
                className="h-full w-full"
                showLegend={true}
              />
            </div>
          </div>
        </div>

        {/* Debug Information */}
        <div className="mt-8 rounded-lg bg-gray-800 p-6">
          <h2 className="mb-4 text-xl font-semibold text-white">
            Debug Information
          </h2>
          <div className="space-y-4 text-sm text-gray-300">
            <div>
              <h3 className="font-semibold text-white">Iran Map Data:</h3>
              <pre className="mt-2 overflow-x-auto rounded bg-gray-700 p-2">
                {JSON.stringify(iranData, null, 2)}
              </pre>
            </div>
            <div>
              <h3 className="font-semibold text-white">World Map Data:</h3>
              <pre className="mt-2 overflow-x-auto rounded bg-gray-700 p-2">
                {JSON.stringify(worldData, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestMapsPage;
