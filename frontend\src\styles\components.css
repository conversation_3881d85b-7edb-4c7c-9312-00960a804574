/* Grid Dashboard Styles */
.grid-container {
  display: grid;
  gap: 1px;
  background-color: #374151; /* gray-700 - darker background to make cells visible */
  padding: 1rem;
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
  border: 2px solid #4b5563; /* gray-600 border */
}

/* Ensure grid cells maintain aspect ratio */
.grid-cell {
  aspect-ratio: 1;
  min-height: 20px;
  min-width: 20px;
  background-color: #1f2937; /* gray-800 */
  border: 1px solid #4b5563; /* gray-600 */
  transition: background-color 0.2s ease;
}

/* Custom grid classes for 28x14 layout */
.grid-cols-28 {
  grid-template-columns: repeat(28, minmax(0, 1fr));
}

.grid-rows-14 {
  grid-template-rows: repeat(14, minmax(0, 1fr));
}

/* Draggable item styles */
.draggable-item {
  position: absolute;
  z-index: 10;
  border-radius: 0.5rem;
  border: 2px solid #3b82f6; /* blue-500 */
  background-color: rgba(59, 130, 246, 0.1); /* blue-600/10 */
  backdrop-filter: blur(4px);
  transition: all 0.2s ease-in-out;
  cursor: move;
}

.draggable-item:hover {
  background-color: rgba(59, 130, 246, 0.2); /* blue-600/20 */
  transform: scale(1.02);
}

.draggable-item.dragging {
  opacity: 0.7;
  transform: scale(1.05);
  z-index: 20;
}

/* Grid cell hover effects */
.grid-cell:hover {
  background-color: #374151; /* gray-700 */
  border-color: #6b7280; /* gray-500 */
}

.grid-cell.occupied {
  background-color: #4b5563; /* gray-600 */
  border-color: #6b7280; /* gray-500 */
}

/* Drop zone indicator */
.drop-zone-valid {
  border: 2px dashed #10b981; /* green-500 */
  background-color: rgba(16, 185, 129, 0.1); /* green-500/10 */
}

.drop-zone-invalid {
  border: 2px dashed #ef4444; /* red-500 */
  background-color: rgba(239, 68, 68, 0.1); /* red-500/10 */
}

/* Remove the grid lines overlay - using individual cell borders instead */

/* Responsive adjustments */
@media (max-width: 1024px) {
  .grid-container {
    padding: 0.5rem;
    height: calc(100vh - 60px);
  }
}

@media (max-width: 768px) {
  .grid-container {
    padding: 0.25rem;
    height: calc(100vh - 50px);
  }
}
