/* IranYekanX */
@font-face {
  font-family: 'IranYekanX';
  src: url('../assets/fonts/iranyekanx/IRANYekanX-Thin.woff2') format('woff2');
  font-weight: 100;
}

@font-face {
  font-family: 'IranYekanX';
  src: url('../assets/fonts/iranyekanx/IRANYekanX-UltraLight.woff2')
    format('woff2');
  font-weight: 200;
}

@font-face {
  font-family: 'IranYekanX';
  src: url('../assets/fonts/iranyekanx/IRANYekanX-Light.woff2') format('woff2');
  font-weight: 300;
}

@font-face {
  font-family: 'IranYekanX';
  src: url('../assets/fonts/iranyekanx/IRANYekanX-Regular.woff2')
    format('woff2');
  font-weight: 400;
}

@font-face {
  font-family: 'IranYekanX';
  src: url('../assets/fonts/iranyekanx/IRANYekanX-Medium.woff2') format('woff2');
  font-weight: 500;
}

@font-face {
  font-family: 'IranYekanX';
  src: url('../assets/fonts/iranyekanx/IRANYekanX-DemiBold.woff2')
    format('woff2');
  font-weight: 600;
}

@font-face {
  font-family: 'IranYekanX';
  src: url('../assets/fonts/iranyekanx/IRANYekanX-Bold.woff2') format('woff2');
  font-weight: 700;
}

@font-face {
  font-family: 'IranYekanX';
  src: url('../assets/fonts/iranyekanx/IRANYekanX-ExtraBold.woff2')
    format('woff2');
  font-weight: 800;
}

@font-face {
  font-family: 'IranYekanX';
  src: url('../assets/fonts/iranyekanx/IRANYekanX-Black.woff2') format('woff2');
  font-weight: 900;
}

@font-face {
  font-family: 'IranYekanX';
  src: url('../assets/fonts/iranyekanx/IRANYekanX-ExtraBlack.woff2')
    format('woff2');
  font-weight: 950;
}

/* IranYekanXFaNum */
@font-face {
  font-family: 'IranYekanXFaNum';
  src: url('../assets/fonts/iranyekanx/IRANYekanXFaNum-Thin.woff2')
    format('woff2');
  font-weight: 100;
}

@font-face {
  font-family: 'IranYekanXFaNum';
  src: url('../assets/fonts/iranyekanx/IRANYekanXFaNum-UltraLight.woff2')
    format('woff2');
  font-weight: 200;
}

@font-face {
  font-family: 'IranYekanXFaNum';
  src: url('../assets/fonts/iranyekanx/IRANYekanXFaNum-Light.woff2')
    format('woff2');
  font-weight: 300;
}

@font-face {
  font-family: 'IranYekanXFaNum';
  src: url('../assets/fonts/iranyekanx/IRANYekanXFaNum-Regular.woff2')
    format('woff2');
  font-weight: 400;
}

@font-face {
  font-family: 'IranYekanXFaNum';
  src: url('../assets/fonts/iranyekanx/IRANYekanXFaNum-Medium.woff2')
    format('woff2');
  font-weight: 500;
}

@font-face {
  font-family: 'IranYekanXFaNum';
  src: url('../assets/fonts/iranyekanx/IRANYekanXFaNum-DemiBold.woff2')
    format('woff2');
  font-weight: 600;
}

@font-face {
  font-family: 'IranYekanXFaNum';
  src: url('../assets/fonts/iranyekanx/IRANYekanXFaNum-Bold.woff2')
    format('woff2');
  font-weight: 700;
}

@font-face {
  font-family: 'IranYekanXFaNum';
  src: url('../assets/fonts/iranyekanx/IRANYekanXFaNum-ExtraBold.woff2')
    format('woff2');
  font-weight: 800;
}

@font-face {
  font-family: 'IranYekanXFaNum';
  src: url('../assets/fonts/iranyekanx/IRANYekanXFaNum-Black.woff2')
    format('woff2');
  font-weight: 900;
}

@font-face {
  font-family: 'IranYekanXFaNum';
  src: url('../assets/fonts/iranyekanx/IRANYekanXFaNum-ExtraBlack.woff2')
    format('woff2');
  font-weight: 950;
}
