/**
 * Format large numbers to abbreviated format
 * Examples:
 * - 234500 → 234k
 * - 1238200 → 1.2M
 * - 5000000000 → 5B
 * @param num - The number to format
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted string
 */
export function formatLargeNumber(num: number, decimals: number = 1): string {
  if (num === 0) return '0';

  const absNum = Math.abs(num);
  const sign = num < 0 ? '-' : '';

  // Define the suffixes and their corresponding values
  const suffixes = [
    { value: 1e12, suffix: 'T' }, // Trillion
    { value: 1e9, suffix: 'B' }, // Billion
    { value: 1e6, suffix: 'M' }, // Million
    { value: 1e3, suffix: 'k' }, // Thousand
  ];

  // Find the appropriate suffix
  for (const { value, suffix } of suffixes) {
    if (absNum >= value) {
      const formatted = (absNum / value).toFixed(decimals);
      // Remove trailing zeros and decimal point if not needed
      const cleanFormatted = parseFloat(formatted).toString();
      return `${sign}${cleanFormatted}${suffix}`;
    }
  }

  // If number is less than 1000, return as is
  return num.toString();
}

/**
 * Format numbers with Persian/Farsi locale
 * @param num - The number to format
 * @returns Formatted string with Persian digits and separators
 */
export function formatNumberPersian(num: number): string {
  return new Intl.NumberFormat('fa-IR').format(num);
}

/**
 * Format large numbers with Persian locale and abbreviation
 * @param num - The number to format
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted string with Persian digits
 */
export function formatLargeNumberPersian(
  num: number,
  decimals: number = 1
): string {
  const formatted = formatLargeNumber(num, decimals);
  // Convert English digits to Persian digits
  return formatted.replace(/\d/g, (digit) => '۰۱۲۳۴۵۶۷۸۹'[parseInt(digit)]);
}
