import html2canvas from 'html2canvas';

/**
 * Takes a screenshot of a DOM element and returns it as a Blob
 * @param element - The DOM element to capture
 * @param options - Additional options for html2canvas
 * @returns Promise<Blob> - The screenshot as a blob
 */
export const captureElementScreenshot = async (
  element: HTMLElement,
  options: {
    backgroundColor?: string;
    scale?: number;
    useCORS?: boolean;
    allowTaint?: boolean;
    quality?: number;
  } = {}
): Promise<Blob> => {
  try {
    const defaultOptions = {
      backgroundColor: '#262626', // Dashboard background color
      scale: 1,
      useCORS: true,
      allowTaint: true, // Allow tainted canvas to handle problematic elements
      foreignObjectRendering: true, // Use foreignObject rendering which is more compatible
      logging: false, // Reduce console noise
      quality: 0.8,
      ...options,
    };

    console.log('Capturing screenshot of element...', defaultOptions);

    const canvas = await html2canvas(element, defaultOptions);

    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            console.log('Screenshot captured successfully', {
              size: blob.size,
              type: blob.type,
            });
            resolve(blob);
          } else {
            reject(new Error('Failed to create blob from canvas'));
          }
        },
        'image/png', // Try PNG format instead of JPEG
        0.95 // High quality for PNG
      );
    });
  } catch (error) {
    console.error('Error capturing screenshot:', error);
    throw new Error('خطا در گرفتن عکس از داشبورد');
  }
};

/**
 * Takes a screenshot of the dashboard grid area
 * @param gridElement - The grid container element (the root dashboard element)
 * @returns Promise<Blob> - The screenshot as a blob
 */
export const captureDashboardScreenshot = async (
  gridElement: HTMLElement
): Promise<Blob> => {
  console.log('captureDashboardScreenshot called with element:', gridElement);
  console.log('Element classes:', gridElement.className);
  console.log('Element dimensions:', {
    width: gridElement.offsetWidth,
    height: gridElement.offsetHeight,
    clientWidth: gridElement.clientWidth,
    clientHeight: gridElement.clientHeight,
    scrollWidth: gridElement.scrollWidth,
    scrollHeight: gridElement.scrollHeight,
  });

  // Find the actual dashboard grid container that contains the widgets
  // Look for the element with grid CSS layout
  const dashboardGrid =
    gridElement.querySelector('.relative.grid') ||
    gridElement.querySelector('[style*="grid-template"]') ||
    gridElement.querySelector('.grid') ||
    gridElement;

  const targetElement = (dashboardGrid as HTMLElement) || gridElement;

  console.log('Target element for screenshot:', {
    element: targetElement,
    classes: targetElement.className,
    dimensions: {
      width: targetElement.offsetWidth,
      height: targetElement.offsetHeight,
      clientWidth: targetElement.clientWidth,
      clientHeight: targetElement.clientHeight,
      scrollWidth: targetElement.scrollWidth,
      scrollHeight: targetElement.scrollHeight,
    },
  });

  // Temporarily modify styles for better screenshot capture
  const rootElement = gridElement;
  const originalOverflow = rootElement.style.overflow;
  const originalMaxHeight = rootElement.style.maxHeight;
  const originalHeight = rootElement.style.height;

  try {
    // Remove overflow constraints for screenshot
    rootElement.style.overflow = 'visible';
    rootElement.style.maxHeight = 'none';

    // Give it a moment for the layout to adjust
    await new Promise((resolve) => setTimeout(resolve, 200));

    return captureElementScreenshot(targetElement, {
      backgroundColor: '#262626', // Dashboard dark background
      scale: 0.6, // Reasonable scale for good quality and file size
      useCORS: true,
      allowTaint: true,
      foreignObjectRendering: true,
      logging: false,
      quality: 0.8,
      // Let html2canvas determine the natural size of the content
      width: targetElement.scrollWidth || targetElement.offsetWidth,
      height: targetElement.scrollHeight || targetElement.offsetHeight,
    });
  } finally {
    // Restore original styles
    rootElement.style.overflow = originalOverflow;
    rootElement.style.maxHeight = originalMaxHeight;
    rootElement.style.height = originalHeight;
  }
};
