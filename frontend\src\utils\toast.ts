import { toast } from 'react-toastify';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import React from 'react';

interface CustomToastProps {
  title: string;
  message: string;
  type: 'success' | 'warning' | 'error';
  onClose?: () => void;
}

const CustomToast: React.FC<CustomToastProps> = ({
  title,
  message,
  type,
  onClose,
}) => {
  const getIcon = () => {
    switch (type) {
      case 'success':
        return React.createElement(CheckCircleIcon, {
          className: 'w-6 h-6 text-green-400',
        });
      case 'warning':
        return React.createElement(ExclamationTriangleIcon, {
          className: 'w-6 h-6 text-yellow-400',
        });
      case 'error':
        return React.createElement(XCircleIcon, {
          className: 'w-6 h-6 text-red-400',
        });
      default:
        return null;
    }
  };

  const getBgColor = () => {
    switch (type) {
      case 'success':
        return 'bg-gradient-to-r from-[#242c32] via-[#242c32] via-[#242c32] to-green-900';
      case 'warning':
        return 'bg-gradient-to-r from-[#242c32] via-[#242c32] via-[#242c32] to-yellow-900';
      case 'error':
        // base color only, overlay handled below
        return 'bg-gradient-to-r from-[#242c32] via-[#242c32] via-[#242c32] to-[#242c32]';
      default:
        return 'bg-[#242c32]';
    }
  };

  const getAccentColor = () => {
    switch (type) {
      case 'success':
        return 'border-b-green-400';
      case 'warning':
        return 'border-b-yellow-400';
      case 'error':
        return 'border-b-red-400';
      default:
        return 'border-b-gray-400';
    }
  };

  return React.createElement(
    'div',
    {
      className: `relative p-4 rounded-lg shadow-lg border-b-4 ${getBgColor()} ${getAccentColor()} min-w-[350px]`,
      style: { fontFamily: 'IranYekanXFaNum, sans-serif' },
    },
    // Overlay only for error type
    type === 'error'
      ? React.createElement('div', {
          className:
            'absolute inset-0 rounded-lg bg-gradient-to-r from-transparent to-[#F04248]/20',
        })
      : null,

    // Close button
    React.createElement(
      'button',
      {
        onClick: onClose,
        className:
          'absolute top-2 left-2 w-6 h-6 rounded-full bg-gray-600/50 flex items-center justify-center hover:bg-gray-600/70 transition-colors',
      },
      React.createElement(XMarkIcon, {
        className: 'w-4 h-4 text-gray-300 cursor-pointer',
      })
    ),

    // Content container
    React.createElement(
      'div',
      { className: 'flex items-start gap-3 pt-2 pl-8 relative z-10' }, // z-10 so above overlay
      // Icon
      React.createElement(
        'div',
        { className: 'flex-shrink-0 flex items-center justify-center' },
        getIcon()
      ),
      // Text
      React.createElement(
        'div',
        { className: 'flex-1 text-white text-right' },
        React.createElement(
          'h4',
          { className: 'font-semibold text-lg mb-1' },
          title
        ),
        React.createElement(
          'p',
          { className: 'text-gray-200 text-sm leading-relaxed' },
          message
        )
      )
    )
  );
};

// Toast utility
const createToast = (
  type: 'success' | 'warning' | 'error',
  title: string,
  message: string
) => {
  const progressClasses = {
    success: 'toast-progress-success',
    warning: 'toast-progress-warning',
    error: 'toast-progress-error',
  };

  toast(
    ({ closeToast }) =>
      React.createElement(CustomToast, {
        title,
        message,
        type,
        onClose: closeToast,
      }),
    {
      closeButton: false,
      hideProgressBar: false,
      className: '!bg-transparent !p-0',
      bodyClassName: '!p-0',
      progressClassName: progressClasses[type],
      progressStyle: { background: 'transparent' },
    }
  );
};

export const showToast = {
  success: (title: string, message: string) =>
    createToast('success', title, message),
  warning: (title: string, message: string) =>
    createToast('warning', title, message),
  error: (title: string, message: string) =>
    createToast('error', title, message),
};
