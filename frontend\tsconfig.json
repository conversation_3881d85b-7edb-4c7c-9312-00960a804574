{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    // REMOVE THESE LINES TO RE-ENABLE ERROR CHECKING:
    "noCheck": true, // Remove this line to enable type checking
    "strict": false, // Change to true or remove to enable strict mode
    "noUnusedLocals": false, // Change to true or remove to check unused variables
    "noUnusedParameters": false, // Change to true or remove to check unused parameters
    "noFallthroughCasesInSwitch": false, // Change to true or remove to check switch fallthrough
    "noImplicitAny": false, // Change to true or remove to require explicit types
    "noImplicitReturns": false, // Change to true or remove to require return statements
    "noImplicitThis": false, // Change to true or remove to require explicit this context
    "strictNullChecks": false, // Change to true or remove to enable null checking
    "strictFunctionTypes": false, // Change to true or remove to enable strict function types
    "strictBindCallApply": false, // Change to true or remove to enable strict bind/call/apply
    "strictPropertyInitialization": false, // Change to true or remove to require property initialization
    // END OF LINES TO REMOVE FOR ERROR CHECKING

    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src"]
}
